import { z } from 'zod';
import { requiredString } from '@/utils/zod';

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

/**
 * Validation schema for forgot password form
 * Validates email format and ensures it's required
 */
export const forgotPasswordSchema = z.object({
  email: requiredString.email('Please enter a valid email address'),
});

/**
 * Validation schema for reset password form (server-side)
 * Validates token and password strength
 * Note: Password confirmation matching is handled client-side
 */
export const resetPasswordSchema = z.object({
  token: requiredString.min(1, 'Reset token is required'),
  password: requiredString.min(8, 'Password must be at least 8 characters long'),
  confirmPassword: requiredString.min(1, 'Please confirm your password'),
});

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type ForgotPasswordValues = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordValues = z.infer<typeof resetPasswordSchema>;

/**
 * Common action result type for auth actions
 */
export type AuthActionResult = {
  success: boolean;
  message: string;
  errors?: Record<string, string[]>;
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Parse FormData to an object for validation
 * @param formData - The FormData to parse
 * @returns Parsed object
 */
export function parseFormDataToObject(formData: FormData): Record<string, string> {
  const data: Record<string, string> = {};
  for (const [key, value] of formData.entries()) {
    if (typeof value === 'string') {
      data[key] = value.trim();
    }
  }
  return data;
}

/**
 * Convert Zod validation errors to field-specific error messages
 * @param errors - Zod validation errors
 * @returns Object with field names as keys and error arrays as values
 */
export function formatZodErrors(errors: z.ZodError): Record<string, string[]> {
  const fieldErrors: Record<string, string[]> = {};
  
  errors.errors.forEach((error) => {
    const fieldName = error.path.join('.');
    if (!fieldErrors[fieldName]) {
      fieldErrors[fieldName] = [];
    }
    fieldErrors[fieldName].push(error.message);
  });
  
  return fieldErrors;
} 