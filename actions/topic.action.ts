'use server';

import { EAPIEndpoint } from "@/@types/enums/api";
import { request } from "@/apis/request";

// Define types for the subject hierarchy data
export type TopicChild = {
  id: string;
  name: string;
  type: string;
  children: TopicChild[];
};

export type TopicHierarchy = {
  id: string;
  name: string;
  type: string;
  children: TopicChild[];
};

/**
 * Server action to fetch subject hierarchy data based on grade and subject
 * @param gradeId - The ID of the selected grade
 * @param subjectId - The ID of the selected subject
 * @returns The subject hierarchy data
 */
export async function fetchSubjectHierarchy(
  gradeId: string,
  topicId: string
) {
  try {
    // Construct the URL with query parameters
    const url = `${EAPIEndpoint.OPTIONS}/subjects/hierarchy?gradeId=${gradeId}&topicId=${topicId}`;


    // Make the API request
    const response = await request<TopicHierarchy[]>({ url });
    if (response.status === 'error') {
      return { 
        data: [], 
        status: 'error', 
        message: response.message || 'Failed to fetch subject hierarchy' 
      };
    }

    return {
      data: response.data,
      status: 'success'
    };
  } catch (error) {
    return { 
      data: [], 
      status: 'error', 
      message: 'An unexpected error occurred' 
    };
  }
}
