'use server';

import { z } from 'zod';
import { forgotPassword, resetPassword } from '@/apis/authApi';
import { 
  forgotPasswordSchema, 
  resetPasswordSchema, 
  AuthActionResult,
  parseFormDataToObject,
  formatZodErrors
} from './auth.schemas';

// ============================================================================
// SERVER ACTIONS
// ============================================================================

/**
 * Server Action for handling forgot password form submissions
 * Validates email input and initiates password reset process
 * 
 * Security Note: Always returns success message to prevent user enumeration
 * 
 * @param prevState - Previous action state (for useFormState)
 * @param formData - Form data containing email
 * @returns Action result with success status and message
 */
export async function forgotPasswordAction(
  prevState: any,
  formData: FormData
): Promise<AuthActionResult> {
  try {
    // Parse FormData to object
    const rawData = parseFormDataToObject(formData);
    
    // Validate input using Zod schema
    const validationResult = forgotPasswordSchema.safeParse(rawData);
    
    if (!validationResult.success) {
      return {
        success: false,
        message: 'Please check your input and try again.',
        errors: formatZodErrors(validationResult.error),
      };
    }
    
    const { email } = validationResult.data;
    await forgotPassword({ email });
    return {
      success: true,
      message: 'Email sent successfully',
    };
    
  } catch (error: any) {
    console.error('Error in forgotPasswordAction:', error);
    return {
      success: true,
      message: 'Email sent successfully',
    };
  }
}

/**
 * Server Action for handling reset password form submissions
 * Validates token and new password, then resets the user's password
 * 
 * @param prevState - Previous action state (for useFormState)
 * @param formData - Form data containing token, password, and confirmPassword
 * @returns Action result with success status and message
 */
export async function resetPasswordAction(
  prevState: any,
  formData: FormData
): Promise<AuthActionResult> {
  try {
    // Parse FormData to object
    const rawData = parseFormDataToObject(formData);
    
    // Validate input using Zod schema
    const validationResult = resetPasswordSchema.safeParse(rawData);
    
    if (!validationResult.success) {
      return {
        success: false,
        message: 'Please check your input and try again.',
        errors: formatZodErrors(validationResult.error),
      };
    }
    
    const { token, password, confirmPassword } = validationResult.data;
    
    // Additional server-side validation: ensure passwords match
    if (password !== confirmPassword) {
      return {
        success: false,
        message: 'Please check your input and try again.',
        errors: { confirmPassword: ["Passwords don't match"] },
      };
    }
    
    // Call the API service function
    const response = await resetPassword({ token,newPassword: password });
    
    if (response.status === 'success') {
      return {
        success: true,
        message: 'Password has been reset successfully. You can now sign in with your new password.',
      };
    } else {
      // Handle error response - message can be string or TValidationError[]
      let errorMessage = 'Failed to reset password. Please try again.';
      
      if (response.message) {
        if (typeof response.message === 'string') {
          errorMessage = response.message;
        } else if (Array.isArray(response.message)) {
          // Extract constraints from TValidationError[]
          errorMessage = response.message.map(err => err.constraints).join(', ') || errorMessage;
        }
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }
    
  } catch (error: any) {
    console.error('Error in resetPasswordAction:', error);
    
    // Handle different types of errors
    if (error.response?.data?.message) {
      let errorMessage = 'An error occurred while resetting your password.';
      
      const apiMessage = error.response.data.message;
      if (typeof apiMessage === 'string') {
        errorMessage = apiMessage;
      } else if (Array.isArray(apiMessage)) {
        errorMessage = apiMessage.map((err: any) => err.constraints).join(', ') || errorMessage;
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }
    
    // Default error for token-related issues
    return {
      success: false,
      message: 'Invalid or expired reset token. Please request a new password reset link.',
    };
  }
} 