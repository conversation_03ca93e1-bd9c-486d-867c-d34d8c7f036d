'use server';

import {
  createPackage,
  getAllPackages,
  getPackageById,
  updatePackage,
  deletePackage,
  createSubscriptionSession,
  getSubscriptionStatus,
  ICreatePackagePayload,
  IUpdatePackagePayload,
  IPackageResponse,
  ICreateSubscriptionSessionPayload,
  ISubscriptionSessionResponse,
  ISubscriptionStatusResponse,
} from '@/apis/packageApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { revalidatePath } from 'next/cache';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';

// Define specific paths for revalidation
const ADMIN_PACKAGES_PATH = '/admin/packages';
const PACKAGE_MANAGEMENT_PATH = '/admin/packages';

/**
 * Fetches all packages.
 * @returns A list of all packages.
 */
export async function handleGetAllPackagesAction(): Promise<TTransformResponse<IPackageResponse[]>> {
  try {
    const response = await getAllPackages();
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Fetches a package by ID.
 * @param packageId - The ID of the package to fetch.
 * @returns The package details.
 */
export async function handleGetPackageByIdAction(packageId: string): Promise<TTransformResponse<IPackageResponse>> {
  try {
    const response = await getPackageById(packageId);
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Creates a new package.
 * @param payload - The package data matching the API structure.
 * @returns The created package.
 */
export async function handleCreatePackageAction(
  payload: ICreatePackagePayload
): Promise<TTransformResponse<IPackageResponse>> {
  try {
    // Ensure the payload matches the API structure
    const cleanedPayload: ICreatePackagePayload = {
      name: payload.name,
      description: payload.description,
      image: payload.image || undefined,
      stripeProductId: payload.stripeProductId || undefined,
      prices: payload.prices || []
    };

    const response = await createPackage(cleanedPayload);

    if (response.status === 'success') {
      // Revalidate the admin packages page
      revalidatePath(ADMIN_PACKAGES_PATH);
      revalidatePath(PACKAGE_MANAGEMENT_PATH);
    }
    
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Updates an existing package.
 * @param packageId - The ID of the package to update.
 * @param payload - The package data to update matching the API structure.
 * @returns The updated package.
 */
export async function handleUpdatePackageAction(
  packageId: string,
  payload: IUpdatePackagePayload
): Promise<TTransformResponse<IPackageResponse>> {
  try {
    // Ensure the payload matches the API structure
    const cleanedPayload: IUpdatePackagePayload = {
      name: payload.name || undefined,
      description: payload.description || undefined,
      image: payload.image || undefined,
      stripeProductId: payload.stripeProductId || undefined,
      prices: payload.prices || undefined
    };

    const response = await updatePackage(packageId, cleanedPayload);

    if (response.status === 'success') {
      // Revalidate the admin packages page
      revalidatePath(ADMIN_PACKAGES_PATH);
      revalidatePath(PACKAGE_MANAGEMENT_PATH);
    }
    
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Deletes a package.
 * @param packageId - The ID of the package to delete.
 * @returns Confirmation of deletion.
 */
export async function handleDeletePackageAction(
  packageId: string
): Promise<TTransformResponse<{ message: string }>> {
  try {
    const response = await deletePackage(packageId);

    if (response.status === 'success') {
      // Revalidate the admin packages page
      revalidatePath(ADMIN_PACKAGES_PATH);
      revalidatePath(PACKAGE_MANAGEMENT_PATH);
    }
    
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Toggles package status (activate/deactivate).
 * @param packageId - The ID of the package to toggle.
 * @param currentlyActive - Whether the package is currently active.
 * @returns A response indicating the result of the toggle operation.
 */
export async function handleTogglePackageStatusAction(
  packageId: string, 
  currentlyActive: boolean
): Promise<TTransformResponse<IPackageResponse | { message: string }>> {
  try {
    let response;
    
    if (currentlyActive) {
      // Deactivate package (soft delete)
      response = await deletePackage(packageId);
    } else {
      // Activate package - Currently no restore endpoint available
      // TODO: Implement restore endpoint in the backend API
      // For now, return an error message indicating this feature needs backend support
      return {
        status: 'error',
        message: 'Package activation feature requires backend API support. Please contact development team.'
      };
    }
    
    if (response.status === 'success') {
      revalidatePath(ADMIN_PACKAGES_PATH);
      revalidatePath(PACKAGE_MANAGEMENT_PATH);
    }
    
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Creates a subscription session for Stripe checkout.
 * @param payload - The subscription session data including packageId, URLs, etc.
 * @returns Stripe checkout session details.
 */
export async function handleCreateSubscriptionSessionAction(
  payload: ICreateSubscriptionSessionPayload
): Promise<TTransformResponse<string>> {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return { status: 'error', message: 'User not authenticated. Please sign in to subscribe.' };
    }
    // Validate required fields
    if (!payload.packageId) {
      return {
        status: 'error',
        message: 'Package ID is required to create subscription session.',
      };
    }

    const response = await createSubscriptionSession(payload);

    if (response.status === 'success') {
      return { status: 'success', data: response.data.url };
    }

    return response;
  } catch (error: any) {
    console.error('Error creating subscription session:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected server error occurred while creating subscription session.',
    };
  }
}

/**
 * Fetches the current user's subscription status.
 * @param accessToken - Optional access token for authentication.
 * @returns The user's subscription status.
 */
export async function handleGetSubscriptionStatusAction(accessToken?: string): Promise<TTransformResponse<ISubscriptionStatusResponse>> {
  try {
    const response = await getSubscriptionStatus(accessToken);
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Fetches all packages (public version for pricing page).
 * This function doesn't require authentication and can be used from client components.
 * @returns A list of all packages.
 */
export async function handleGetPackagesPublicAction(): Promise<TTransformResponse<IPackageResponse[]>> {
  try {
    // Use the same API call but without authentication requirements
    const response = await getAllPackages();
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred while fetching packages.' };
  }
} 