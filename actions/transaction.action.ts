import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';
import { request } from '@/apis/request';

// ============================================================================
// TYPESCRIPT INTERFACES
// ============================================================================

export interface ITransaction {
  id: string;
  date: string; // ISO date string
  description: string;
  amount: number; // in cents
  currency: string;
  status: 'paid' | 'failed' | 'pending';
  paymentMethod: string;
  invoiceUrl?: string;
  receiptUrl?: string;
  type?: string; // additional field for transaction type
}

export interface ITransactionFilters {
  startDate?: string; // ISO date string
  endDate?: string; // ISO date string
  status?: 'paid' | 'failed' | 'pending' | 'all';
  page?: number;
  limit?: number;
}

export interface ITransactionPagination {
  items: ITransaction[];
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface ITransactionActionResult {
  success: boolean;
  data?: ITransactionPagination | ITransaction;
  message?: string;
  errors?: Record<string, string[]>;
}

// ============================================================================
// ZOD VALIDATION SCHEMAS
// ============================================================================

export const transactionFiltersSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  status: z.enum(['paid', 'failed', 'pending', 'all']).optional().default('all'),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: "Start date must be before or equal to end date",
  path: ["endDate"],
});

export const transactionIdSchema = z.object({
  transactionId: z.string().min(1, "Transaction ID is required"),
});

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function formatZodErrors(error: z.ZodError): Record<string, string[]> {
  const errors: Record<string, string[]> = {};
  error.errors.forEach((err) => {
    const path = err.path.join('.');
    if (!errors[path]) {
      errors[path] = [];
    }
    errors[path].push(err.message);
  });
  return errors;
}

// ============================================================================
// SERVER ACTIONS
// ============================================================================

/**
 * Server Action for fetching user transaction history
 * Supports filtering by date range and status, with pagination
 *
 * @param filters - Transaction filters including pagination and date range
 * @returns Transaction history with pagination metadata
 */
export async function getTransactions(
  filters: ITransactionFilters = {}
): Promise<ITransactionActionResult> {
  'use server';
  try {
    // Get authenticated user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return {
        success: false,
        message: 'Authentication required. Please sign in to view transaction history.',
      };
    }

    // Validate input parameters
    const validationResult = transactionFiltersSchema.safeParse(filters);
    if (!validationResult.success) {
      return {
        success: false,
        message: 'Invalid filter parameters. Please check your input.',
        errors: formatZodErrors(validationResult.error),
      };
    }

    const validatedFilters = validationResult.data;

    // Build query parameters for the API request
    const queryParams = new URLSearchParams();
    queryParams.append('page', validatedFilters.page.toString());
    queryParams.append('limit', validatedFilters.limit.toString());
    queryParams.append('userId', session.user.id);

    if (validatedFilters.startDate) {
      queryParams.append('startDate', validatedFilters.startDate);
    }
    if (validatedFilters.endDate) {
      queryParams.append('endDate', validatedFilters.endDate);
    }
    if (validatedFilters.status && validatedFilters.status !== 'all') {
      queryParams.append('status', validatedFilters.status);
    }

    // Make API request to fetch transactions
    const response = await request<ITransactionPagination>({
      url: `/transactions?${queryParams.toString()}`,
      options: {
        method: 'GET',
      },
    });

    // Handle API response
    if (response.status === 'success') {
      return {
        success: true,
        data: response.data,
      };
    } else {
      // Handle error response - response.message could be string or TValidationError[]
      let errorMessage = 'Failed to fetch transaction history. Please try again.';
      
      if (response.message) {
        if (typeof response.message === 'string') {
          errorMessage = response.message;
        } else if (Array.isArray(response.message)) {
          // Extract constraints from TValidationError[]
          errorMessage = response.message.map(err => err.constraints).join(', ') || errorMessage;
        }
      }

      return {
        success: false,
        message: errorMessage,
      };
    }

  } catch (error: any) {
    console.error('Error in getTransactions server action:', error);
    
    // Handle different types of errors
    if (error.response?.data?.message) {
      let errorMessage = 'An error occurred while fetching transaction history.';
      
      const apiMessage = error.response.data.message;
      if (typeof apiMessage === 'string') {
        errorMessage = apiMessage;
      } else if (Array.isArray(apiMessage)) {
        errorMessage = apiMessage.map((err: any) => err.constraints).join(', ') || errorMessage;
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred while fetching transaction history. Please try again later.',
    };
  }
}

/**
 * Server Action for fetching a single transaction by ID
 *
 * @param transactionId - The ID of the transaction to fetch
 * @returns Single transaction details
 */
export async function getTransactionById(
  transactionId: string
): Promise<ITransactionActionResult> {
  'use server';
  try {
    // Get authenticated user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return {
        success: false,
        message: 'Authentication required. Please sign in to view transaction details.',
      };
    }

    // Validate transaction ID
    const validationResult = transactionIdSchema.safeParse({ transactionId });
    if (!validationResult.success) {
      return {
        success: false,
        message: 'Invalid transaction ID provided.',
        errors: formatZodErrors(validationResult.error),
      };
    }

    // Make API request to fetch single transaction
    const response = await request<ITransaction>({
      url: `/transactions/${transactionId}?userId=${session.user.id}`,
      options: {
        method: 'GET',
      },
    });

    // Handle API response
    if (response.status === 'success') {
      return {
        success: true,
        data: response.data,
      };
    } else {
      // Handle error response
      let errorMessage = 'Failed to fetch transaction details. Please try again.';
      
      if (response.message) {
        if (typeof response.message === 'string') {
          errorMessage = response.message;
        } else if (Array.isArray(response.message)) {
          errorMessage = response.message.map(err => err.constraints).join(', ') || errorMessage;
        }
      }

      return {
        success: false,
        message: errorMessage,
      };
    }

  } catch (error: any) {
    console.error('Error in getTransactionById server action:', error);
    
    // Handle different types of errors
    if (error.response?.data?.message) {
      let errorMessage = 'An error occurred while fetching transaction details.';
      
      const apiMessage = error.response.data.message;
      if (typeof apiMessage === 'string') {
        errorMessage = apiMessage;
      } else if (Array.isArray(apiMessage)) {
        errorMessage = apiMessage.map((err: any) => err.constraints).join(', ') || errorMessage;
      }
      
      return {
        success: false,
        message: errorMessage,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred while fetching transaction details. Please try again later.',
    };
  }
}