'use server';

import { request } from '@/apis/request';
import { TTransformResponse } from '@/apis/transformResponse';
import { revalidatePath } from 'next/cache';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';

// Define interfaces for API data structures
interface ISchool {
  id: string;
  name: string;
}

interface IUploadFormatResponse {
  id: string;
  schoolId: string;
  filename: string;
  uploadedAt: string;
}

interface IGetFormatResponse {
  text?: string;
  url?: string;
  contentType?: string;
  blob?: Blob;
}

// fetchSchoolsAction has been removed as a similar function handleGetAllSchoolsAction exists in actions/school.action.ts

/**
 * Uploads an examination format for a school
 * @param formData - The form data containing the file and schoolId
 * @returns The uploaded examination format
 */
export async function uploadExaminationFormatAction(
  formData: FormData
): Promise<TTransformResponse<IUploadFormatResponse>> {
  try {
    const response = await request<IUploadFormatResponse>({
      url: '/schools/examination-format',
      options: {
        method: 'POST',
        body: formData,
      },
      noContentType: true, // Important for FormData
    });

    if (response.status === 'success') {
      // Revalidate the examination formats page
      revalidatePath('/examination-formats');
    }

    return response;
  } catch (error: any) {
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while uploading the examination format.' 
    };
  }
}

/**
 * Gets the examination format for a school
 * @param schoolId - The ID of the school
 * @returns The examination format
 */
export async function getExaminationFormatAction(
  schoolId: string
): Promise<TTransformResponse<IGetFormatResponse>> {
  try {
    // Get the session to extract the accessToken
    const session = await getServerSession(authOptions);
    const accessToken = session?.user?.accessToken;
    
    if (!accessToken) {
      return {
        status: 'error',
        message: 'Authentication token is missing. Please log in again.'
      };
    }

    // We need to handle the response differently based on the content type
    // So we'll use fetch directly instead of the request helper
    const baseUrl = process.env.API_URL || '';
    const response = await fetch(`${baseUrl}/schools/${schoolId}/examination-format`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
    });

    // Check if the response is OK
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return { 
        status: 'error', 
        message: errorData.message || `Error: ${response.status} ${response.statusText}` 
      };
    }

    // Check the content type to determine how to handle the response
    const contentType = response.headers.get('Content-Type');
    
    if (contentType?.includes('application/pdf')) {
      // For server actions, we can't return a Blob directly as it's not serializable
      // Instead, we'll convert it to a base64 string
      const arrayBuffer = await response.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString('base64');
      
      return { 
        status: 'success', 
        data: { 
          url: `data:application/pdf;base64,${base64}`,
          contentType: 'application/pdf'
        } 
      };
    } else if (contentType?.includes('text/plain')) {
      // Handle text response
      const text = await response.text();
      return { 
        status: 'success', 
        data: { 
          text,
          contentType: 'text/plain'
        } 
      };
    } else {
      // Handle JSON or other response types
      const data = await response.json();
      return { 
        status: 'success', 
        data: { 
          text: data.text || JSON.stringify(data),
          contentType: contentType || 'application/json'
        } 
      };
    }
  } catch (error: any) {
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while fetching the examination format.' 
    };
  }
}

/**
 * Deletes the examination format for a school
 * @param schoolId - The ID of the school
 * @returns Success or error status
 */
export async function deleteExaminationFormatAction(
  schoolId: string
): Promise<TTransformResponse<any>> {
  try {
    const response = await request<any>({
      url: `/schools/${schoolId}/examination-format`,
      options: {
        method: 'DELETE',
      },
    });

    if (response.status === 'success') {
      // Revalidate the examination formats page
      revalidatePath('/examination-formats');
    }

    return response;
  } catch (error: any) {
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while deleting the examination format.' 
    };
  }
}
