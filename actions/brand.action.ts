'use server';

import {
  createBrand,
  getAllBrands,
  updateBrand,
  ICreateBrandPayload,
  IUpdateBrandPayload,
  IBrandResponse
} from '@/apis/brandApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { revalidatePath } from 'next/cache';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';

// Define specific paths for revalidation
const USERS_MANAGEMENT_PATH = '/users-management';
const USER_CREATE_PATH = '/users-management/create';

/**
 * Fetches all brands.
 * @returns A list of all brands.
 */
export async function handleGetAllBrandsAction(): Promise<TTransformResponse<IBrandResponse[]>> {
  try {
    const response = await getAllBrands();
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Creates a new brand and updates the session if the user has a school.
 * @param payload - The brand data.
 * @param updateSession - Optional function to update the session (for client-side usage)
 * @returns The created brand.
 */
export async function handleCreateBrandAction(
  payload: ICreateBrandPayload,
  updateSession?: (data: any) => Promise<void>
): Promise<TTransformResponse<IBrandResponse>> {
  try {
    // Ensure optional fields are truly optional or pass undefined if empty string
    const cleanedPayload: ICreateBrandPayload = {
      logo: payload.logo || undefined,
      color: payload.color || undefined,
      image: payload.image || undefined,
    };

    const response = await createBrand(cleanedPayload);

    if (response.status === 'success' && response.data) {
      // Get current session to check if user has a school
      const session = await getServerSession(authOptions);

      // If user has a school and updateSession is provided, update the session with brand data
      if (session?.user?.school && updateSession) {
        try {
          await updateSession({
            user: {
              school: {
                ...session.user.school,
                brand: {
                  id: response.data.id,
                  logo: response.data.logo,
                  color: response.data.color,
                  image: response.data.image,
                }
              }
            }
          });
        } catch (sessionError) {
        }
      }

      // Revalidate the users management and user creation pages
      revalidatePath(USERS_MANAGEMENT_PATH);
      revalidatePath(USER_CREATE_PATH);
    }
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Updates an existing brand and updates the session if the user has a school with this brand.
 * @param brandId - The ID of the brand to update.
 * @param payload - The brand data to update.
 * @param updateSession - Optional function to update the session (for client-side usage)
 * @returns The updated brand.
 */
export async function handleUpdateBrandAction(
  brandId: string,
  payload: IUpdateBrandPayload,
  updateSession?: (data: any) => Promise<void>
): Promise<TTransformResponse<IBrandResponse>> {
  try {
    // Ensure optional fields are truly optional or pass undefined if empty string
    const cleanedPayload: IUpdateBrandPayload = {
      logo: payload.logo || undefined,
      color: payload.color || undefined,
      image: payload.image || undefined,
    };

    const response = await updateBrand(brandId, cleanedPayload);

    if (response.status === 'success' && response.data) {
      // Get current session to check if user has a school with this brand
      const session = await getServerSession(authOptions);

      // If user has a school with this brand and updateSession is provided, update the session
      if (session?.user?.school?.brand?.id === brandId && updateSession) {
        try {
          await updateSession({
            user: {
              school: {
                ...session.user.school,
                brand: {
                  id: response.data.id,
                  logo: response.data.logo,
                  color: response.data.color,
                  image: response.data.image,
                }
              }
            }
          });
        } catch (sessionError) {}
      }

      // Revalidate the users management and user creation pages
      revalidatePath(USERS_MANAGEMENT_PATH);
      revalidatePath(USER_CREATE_PATH);
    }
    return response;
  } catch (error: any) {
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}
