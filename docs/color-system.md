# Color System Documentation

## Overview

This document describes the comprehensive color system implemented for the EduSG application using Tailwind CSS v4's `@theme` directive and CSS custom properties. The system provides a consistent, accessible, and maintainable approach to color usage throughout the application.

## Architecture

The color system is built on three layers:

1. **Base Color Palette** - Fundamental colors defined as CSS custom properties
2. **Semantic Color Tokens** - Purpose-driven color mappings that reference base colors
3. **Component Integration** - Tailwind utility classes generated from the semantic tokens

## Base Color Palette

### Primary Colors
```css
--color-black: #000000;
--color-white: #ffffff;
```

### Blue Accent Palette
```css
--color-blue-50: #eff6ff;   /* Lightest blue for subtle backgrounds */
--color-blue-100: #dbeafe;  /* Light blue for highlights */
--color-blue-200: #bfdbfe;  /* Medium-light blue for hover states */
--color-blue-500: #3b82f6;  /* Standard blue for interactive elements */
--color-blue-600: #2563eb;  /* Primary blue for links and actions */
--color-blue-800: #1e40af;  /* Dark blue for emphasis */
```

### Gray Neutrals
```css
--color-gray-50: #f9fafb;   /* Subtle background */
--color-gray-100: #f3f4f6;  /* Alternating sections */
--color-gray-600: #4b5563;  /* Secondary text */
--color-gray-800: #1f2937;  /* Primary text */
```

## Semantic Color Tokens

### Action & Interactive Colors
- `--color-primary-action`: Primary buttons, headers, main actions
- `--color-link-default`: Links and interactive text elements
- `--color-link-hover`: Hover state for links and interactive elements

### Background Colors
- `--color-background-default`: Main page backgrounds, contrast areas
- `--color-background-subtle`: Subtle background areas, cards
- `--color-section-bg-accent`: Section backgrounds with blue tint
- `--color-section-bg-neutral-alt`: Alternating section backgrounds
- `--color-accent-bg-light`: Light accent backgrounds, highlights

### Text Colors
- `--color-text-primary`: Main body text, headings
- `--color-text-secondary`: Secondary text, captions, labels

### Component-Specific Colors
- `--color-button-pill-bg`: Pill buttons and tags background
- `--color-button-pill-text`: Pill buttons and tags text
- `--color-tag-bg`: Tag component backgrounds
- `--color-tag-text`: Tag component text
- `--color-interactive-hover-bg-light`: Light hover backgrounds

## Usage Guidelines

### Tailwind Utility Classes

The semantic tokens automatically generate Tailwind utility classes:

```html
<!-- Backgrounds -->
<div class="bg-primary-action">Primary action button</div>
<div class="bg-background-subtle">Subtle background</div>
<div class="bg-section-bg-accent">Accented section</div>

<!-- Text -->
<p class="text-text-primary">Primary text content</p>
<span class="text-text-secondary">Secondary information</span>
<a class="text-link-default hover:text-link-hover">Interactive link</a>

<!-- Borders -->
<div class="border-gray-200">Standard border</div>
```

### Component Examples

#### Button Variants
```tsx
// Primary action button
<button className="bg-primary-action text-background-default">
  Primary Action
</button>

// Pill button
<button className="bg-button-pill-bg text-button-pill-text rounded-full">
  Pill Button
</button>

// Link-style button
<button className="text-link-default hover:text-link-hover">
  Link Button
</button>
```

#### Tag Components
```tsx
// Default tag
<span className="bg-section-bg-neutral-alt text-text-primary">
  Default Tag
</span>

// Accent tag
<span className="bg-accent-bg-light text-link-default">
  Accent Tag
</span>

// Pill tag
<span className="bg-tag-bg text-tag-text">
  Pill Tag
</span>
```

## Accessibility Compliance

All color combinations meet WCAG AA accessibility standards:

### Contrast Ratios
- **Primary text on default background**: 4.5:1 minimum
- **Secondary text on default background**: 4.5:1 minimum
- **Button text on button backgrounds**: 4.5:1 minimum
- **Interactive elements**: 3:1 minimum for large text

### Testing Tools
- Use browser dev tools accessibility panel
- Axe DevTools extension
- WAVE accessibility checker
- Manual contrast ratio verification

## DaisyUI Integration

The color system integrates with DaisyUI v5 through theme mapping:

```css
@plugin "daisyui/theme" {
  --color-primary: var(--color-primary-action);
  --color-accent: var(--color-link-default);
  --color-base-100: var(--color-background-default);
  --color-base-content: var(--color-text-primary);
  /* ... additional mappings */
}
```

## Migration Guide

### From Old System
1. Replace hardcoded color classes with semantic tokens
2. Update component variants to use new color system
3. Test accessibility compliance
4. Verify visual consistency

### Common Replacements
```css
/* Old → New */
bg-gray-100 → bg-section-bg-neutral-alt
text-gray-800 → text-text-primary
text-gray-600 → text-text-secondary
text-blue-600 → text-link-default
bg-blue-100 → bg-accent-bg-light
```

## Best Practices

1. **Always use semantic tokens** instead of base colors directly
2. **Test color combinations** for accessibility compliance
3. **Maintain consistency** across similar UI elements
4. **Document custom color usage** when extending the system
5. **Use hover states** for interactive elements
6. **Consider dark mode** when extending the system

## File Locations

- **Main CSS**: `app/globals.css`
- **Component styles**: Individual component files
- **Documentation**: `docs/color-system.md`

## Support

For questions about the color system or accessibility compliance, refer to:
- WCAG 2.1 AA Guidelines
- Tailwind CSS v4 Documentation
- DaisyUI v5 Theming Guide
