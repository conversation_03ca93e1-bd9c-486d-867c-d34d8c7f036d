import { EUserRole } from '@/config/enums/user';
import { request } from './request';
import { TTransformResponse } from './transformResponse';

export interface ICreateUserPayload {
  name: string;
  email: string;
  password: string;
  role: EUserRole;
  schoolId?: string;
}

export interface IUpdateUserPayload {
  name?: string;
  email?: string;
  password?: string;
  role?: EUserRole;
  schoolId?: string;
}

export interface IUserResponse {
  id: string;
  name: string;
  email: string;
  role: EUserRole;
  schoolId?: string | null;
  // Add other fields if returned by the API
}

const USER_API_ENDPOINT = '/user'; // As per documentation

/**
 * Creates a new user.
 * Corresponds to: POST /user
 * @param data - The user data.
 * @returns The created user profile.
 */
export async function createUser(payload: ICreateUserPayload): Promise<{ status: 'success' | 'error'; data?: IUserResponse; message?: string | any[] }> {
  try {
    const response = await request<IUserResponse>({
      url: USER_API_ENDPOINT,
      options: {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    });

    if (response.status === 'success') {
      if (response.data && 'id' in response.data) {
         return { status: 'success', data: response.data as IUserResponse };
      }
      return { status: 'success', data: response.data };
    } else {
      return { status: 'error', message: response.message };
    }
  } catch (error: any) {
    console.error('Error creating user:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while creating the user.' };
  }
}

/**
 * Gets the current user's profile.
 * Corresponds to: GET /user/me
 * @returns The current user's profile.
 */
export async function getCurrentUser(): Promise<TTransformResponse<IUserResponse>> {
  try {
    const response = await request<IUserResponse>({
      url: `${USER_API_ENDPOINT}/me`,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching current user:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching your profile.' };
  }
}

/**
 * Fetches all users.
 * Corresponds to: GET /user
 * @param schoolId - Optional school ID to filter users.
 * @param role - Optional user role to filter users.
 * @returns A list of all users.
 */
export async function getAllUsers(schoolId?: string, role?: EUserRole): Promise<TTransformResponse<IUserResponse[]>> {
  try {
    const params = new URLSearchParams();
    if (schoolId) {
      params.append('schoolId', schoolId);
    }
    if (role) {
      params.append('role', role);
    }
    
    let url = USER_API_ENDPOINT;
    const queryString = params.toString();
    if (queryString) {
      url += `?${queryString}`;
    }

    const response = await request<IUserResponse[]>({
      url,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching users:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching users.' };
  }
}

/**
 * Fetches a user by ID.
 * Corresponds to: GET /user/{id}
 * @param userId - The ID of the user to fetch.
 * @returns The user details.
 */
export async function getUserById(userId: string): Promise<TTransformResponse<IUserResponse>> {
  try {
    const response = await request<IUserResponse>({
      url: `${USER_API_ENDPOINT}/${userId}`,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching user:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching the user.' };
  }
}

/**
 * Updates a user.
 * Corresponds to: PATCH /user/{id}
 * @param userId - The ID of the user to update.
 * @param payload - The updated user data.
 * @returns The updated user details.
 */
export async function updateUser(userId: string, payload: IUpdateUserPayload): Promise<TTransformResponse<IUserResponse>> {
  try {
    const response = await request<IUserResponse>({
      url: `${USER_API_ENDPOINT}/${userId}`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error updating user:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while updating the user.' };
  }
}

/**
 * Deletes a user.
 * Corresponds to: DELETE /user/{id}
 * @param userId - The ID of the user to delete.
 * @returns Success or error status.
 */
export async function deleteUser(userId: string): Promise<TTransformResponse<any>> {
  try {
    const response = await request<any>({
      url: `${USER_API_ENDPOINT}/${userId}`,
      options: {
        method: 'DELETE',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error deleting user:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while deleting the user.' };
  }
}
