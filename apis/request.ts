import { transformResponse } from './transformResponse';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';
import { logRBACViolation } from '@/utils/rbacErrorHandler';

export const request = async <T>({
  url,
  options,
  noContentType = false,
  isPublic = false,
}: {
  url: string;
  options?: RequestInit;
  noContentType?: boolean;
  isPublic?: boolean;
}) => {
  const baseUrl = process.env.API_URL + url;
  // Get the session to extract the accessToken
  const session = await getServerSession(authOptions);
  const accessToken = session?.user?.accessToken;

  const headers: RequestInit = {
    headers: {
      ...(noContentType ? {} : { 'Content-Type': 'application/json' }),
      ...(accessToken && !isPublic ? { 'Authorization': `Bearer ${accessToken}` } : {}),
      ...options?.headers,
    },
  };


  const response = await fetch(baseUrl, { next: { revalidate: 10 }, ...options, ...headers });


  const result = await transformResponse<T>(response);

  // Handle 403 Forbidden responses with enhanced logging
  if (result.status === 'error' && result.statusCode === 403) {
    // Log RBAC violation for security monitoring
    logRBACViolation({
      userId: session?.user?.id,
      userRole: session?.user?.role,
      attemptedAction: `${options?.method || 'GET'} ${url}`,
      resource: url,
    });
  }

  // Handle 401 Unauthorized responses (token expired/invalid)
  if (result.status === 'error' && result.statusCode === 401) {
    console.warn('[API Request] Unauthorized access - token may be expired or invalid');
    // You could trigger a sign-out or token refresh here if needed
  }

  return result;
};
