// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { TBranch } from '@/lib/types/entities/branch';
import type { TRoles, TResource } from '@/lib/types/entities/resource';
import 'next-auth';
import 'next-auth/core/types';
import 'next-auth/jwt';

declare module 'next-auth' {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */

  interface ISubscription {
    id: string;
    userId: string;
    packageId: string;
    package: IPackageResponse;
    stripeSubscriptionId?: string;
    stripeCustomerId?: string;
    status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
    currentPeriodStart?: string;
    currentPeriodEnd?: string;
    cancelAtPeriodEnd?: boolean;
    canceledAt?: string;
    trialStart?: string;
    trialEnd?: string;
    createdAt: string;
    updatedAt: string;
  }

  // Minimal subscription data for session storage to reduce cookie size
  interface ISubscriptionSummary {
    status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
    packageId: string;
    packageName: string;
  }

  interface User {
    id: string;
    name: string;
    email: string;
    accessToken?: string;
    role: string;
    schoolId: string | null;
    isLogin?: boolean;
    subscription?: ISubscription;
    school?: {
      id: string;
      name: string;
      address: string;
      phoneNumber: string;
      registeredNumber: string;
      email: string;
      brand?: { // Make brand itself optional to align with ISchoolResponse
        id: string;
        logo?: string; // Make optional
        color?: string; // Make optional
        image?: string; // Make optional
      }
    }
  }

  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      accessToken?: string;
      role: string;
      schoolId: string | null;
      isLogin?: boolean;
      subscription?: ISubscriptionSummary;
      school?: {
        id: string;
        name: string;
        address: string;
        phoneNumber: string;
        registeredNumber: string;
        email: string;
        brand?: { // Make brand itself optional
          id: string;
          logo?: string; // Make optional
          color?: string; // Make optional
          image?: string; // Make optional
        }
      }
    };
  }
}

declare module 'next-auth/jwt' {
  // eslint-disable-next-line no-shadow
  interface JWT {
    /** JWT token properties */
    id?: string;
    name?: string;
    email?: string;
    role?: string;
    schoolId?: string | null;
    accessToken?: string;
    isLogin?: boolean;
    school?: {
      id: string;
      name: string;
      address: string;
      phoneNumber: string;
      registeredNumber: string;
      email: string;
      brand?: { // Make brand itself optional
        id: string;
        logo?: string; // Make optional
        color?: string; // Make optional
        image?: string; // Make optional
      }
    }
    subscription?: ISubscription;
  }
}

declare module 'next-auth/core/types' {
  // eslint-disable-next-line no-shadow
  interface User {
    /** Core user properties */
    id?: string;
    name?: string;
    email?: string;
    role?: string;
    schoolId?: string | null;
    accessToken?: string;
    isLogin?: boolean;
    school?: {
      id: string;
      name: string;
      address: string;
      phoneNumber: string;
      registeredNumber: string;
      email: string;
      brand?: { // Make brand itself optional
        id: string;
        logo?: string; // Make optional
        color?: string; // Make optional
        image?: string; // Make optional
      }
    }
    subscription?: ISubscription;
  }
}
