/**
 * Comprehensive TypeScript interfaces and DTOs for Worksheet Management
 * Based on the worksheet-management-endpoints.md specification
 */

// ============================================================================
// ENUMS
// ============================================================================

export enum EQuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  TRUE_FALSE = 'true_false',
  FILL_IN_THE_BLANK = 'fill_in_the_blank',
  SHORT_ANSWER = 'short_answer',
  ESSAY = 'essay',
  MATCHING = 'matching',
  ORDERING = 'ordering',
  CALCULATION = 'calculation',
  DIAGRAM = 'diagram',
  LONG_ANSWER = 'long_answer'
}

export enum EQuestionDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard'
}

export enum ESelectionStrategy {
  POOL_ONLY = 'pool-only',
  AI_ONLY = 'ai-only',
  HYBRID = 'hybrid',
  MIXED = 'mixed'
}

export enum EWorksheetStatus {
  PENDING = 'Pending',
  GENERATED = 'Generated',
  ERROR = 'Error'
}

// ============================================================================
// CORE INTERFACES
// ============================================================================

export interface ISubjectItemDto {
  label: string;
  items: string[];
}

export interface IQuestionTypeDto {
  label: string;
  count: number;
}

export interface IWorksheetOptionDto {
  key: string;
  value: string;
  text?: string;
}

export interface IQuestionMedia {
  imageUrl?: string;
  imagePrompt?: string;
  audioUrl?: string;
  videoUrl?: string;
  attachmentUrls?: string[];
}

export interface IQuestionMetadata {
  tags?: string[];
  keywords?: string[];
  estimatedTimeMinutes?: number;
  cognitiveLevel?: string;
  learningObjectives?: string[];
  prerequisites?: string[];
  hints?: string[];
  references?: string[];
}

// ============================================================================
// REQUEST DTOs
// ============================================================================

export interface ICreateWorksheetDto {
  title?: string;
  description?: string;
  grade?: string;
  topic?: string;
  subject?: ISubjectItemDto[];
  level?: string;
  language?: string;
  question_count?: string;
  isCustomQuestionCount?: boolean;
  question_type?: IQuestionTypeDto[];
  options?: IWorksheetOptionDto[];
  questionSourceStrategy?: ESelectionStrategy;
}

export interface IGetWorksheetsQuery {
  page?: number;
  limit?: number;
  search?: string;
  grade?: string;
  subject?: string;
  topic?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface IAddQuestionToWorksheetDto {
  // Core question fields
  type: EQuestionType;
  content: string;
  options: string[];
  answer: string[];
  explain: string;
  
  // Optional worksheet-specific fields
  position?: number;
  points?: number;
  optionTypeId?: string;
  optionValueId?: string;
  questionPoolId?: string;
  
  // Optional metadata
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  topic?: string;
  subtopic?: string;
  grade?: string;
  difficulty?: EQuestionDifficulty;
  
  // Media and metadata
  media?: IQuestionMedia;
  metadata?: IQuestionMetadata;
}

export interface IUpdateWorksheetQuestionDto extends Partial<IAddQuestionToWorksheetDto> {
  version?: number;
  updateReason?: string;
}

export interface IReplaceWorksheetQuestionDto extends IAddQuestionToWorksheetDto {
  version: number;
  updateReason?: string;
}

export interface IBulkReorderQuestionsDto {
  reorders: {
    questionId: string;
    newPosition: number;
  }[];
}

export interface IBulkAddQuestionsDto {
  questions: IAddQuestionToWorksheetDto[];
  insertPosition?: number;
  validateQuestions?: boolean;
  reason?: string;
}

export interface IBulkRemoveQuestionsDto {
  questionIds: string[];
  reason?: string;
  forceRemoval?: boolean;
}

export interface IBulkUpdateQuestionsDto {
  updates: {
    questionId: string;
    updates: IUpdateWorksheetQuestionDto;
  }[];
  reason?: string;
  validateQuestions?: boolean;
}

export interface IAutoFillQuestionsDto {
  questionCount?: number;
  difficultyOverride?: string;
  questionTypesOverride?: string[];
}

// ============================================================================
// RESPONSE DTOs
// ============================================================================

export interface IQuestionResponse {
  id: string;
  type: EQuestionType;
  content: string;
  options: string[];
  answer: string[];
  explain: string;
  position?: number;
  points?: number;
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  topic?: string;
  subtopic?: string;
  grade?: string;
  difficulty?: EQuestionDifficulty;
  media?: IQuestionMedia;
  metadata?: IQuestionMetadata;
  createdAt: string;
  updatedAt: string;
  version?: number;
}

export interface IWorksheetResponse {
  id: string;
  title: string;
  description?: string;
  grade?: string;
  topic?: string;
  subject?: ISubjectItemDto[];
  level?: string;
  language?: string;
  questionCount: number;
  questions: IQuestionResponse[];
  questionIds?: string[]; // Array of question IDs for proper ordering
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  schoolId?: string;
  generatingStatus?: EWorksheetStatus;
}

export interface IPaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface IPaginatedWorksheetsResponse {
  data: IWorksheetResponse[];
  pagination: IPaginationMeta;
}

export interface IBulkOperationResponseDto {
  success: boolean;
  successCount: number;
  failureCount: number;
  totalCount: number;
  successes?: any[];
  failures?: {
    item?: any;
    questionId?: string;
    error: string;
    index: number;
  }[];
  timestamp: string;
  processingTimeMs: number;
}

export interface ICacheMetrics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  cacheSize: number;
  lastWarmingTime: string;
}

export interface IDeleteResponse {
  success: boolean;
  message: string;
}

export interface IReplaceWithSimilarResponse {
  success: boolean;
  message: string;
  data: {
    worksheetId: string;
    oldQuestionId: string;
    newQuestion: IQuestionResponse;
    position: number;
    totalQuestions: number;
  };
}

export interface IAutoFillQuestionsResponse {
  success: boolean;
  questionsAdded: number;
  totalQuestions: number;
  message: string;
  addedQuestions: IQuestionResponse[];
}

// Enhanced response that includes complete worksheet data after auto-fill
export interface IAutoFillQuestionsEnhancedResponse extends IAutoFillQuestionsResponse {
  worksheetData: {
    questions: IQuestionResponse[];
    questionIds?: string[];
  };
}

export interface IErrorResponse {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}

// ============================================================================
// LEGACY COMPATIBILITY (for existing codebase)
// ============================================================================

export interface TWorksheetLegacy {
  id: string;
  title: string;
  description: string;
  generatingStatus: EWorksheetStatus;
  createdAt: string;
  deletedAt: string;
  selectedOptions: {
    createdAt: string;
    id: string;
    optionType: { key: string; label: string; description: string; };
    optionValue: { label: string; value: string; };
    text: string;
  }[];
  promptResult: { result: IQuestionResponse[]; };
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export interface IWorksheetFilters {
  grade?: string;
  subject?: string;
  topic?: string;
  level?: string;
  language?: string;
  status?: EWorksheetStatus;
  createdBy?: string;
  schoolId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface IQuestionFilters {
  type?: EQuestionType;
  difficulty?: EQuestionDifficulty;
  subject?: string;
  topic?: string;
  grade?: string;
  hasMedia?: boolean;
  pointsMin?: number;
  pointsMax?: number;
}

export interface IWorksheetSortOptions {
  field: 'title' | 'createdAt' | 'updatedAt' | 'questionCount' | 'grade';
  direction: 'asc' | 'desc';
}

export interface IQuestionSortOptions {
  field: 'position' | 'createdAt' | 'updatedAt' | 'points' | 'difficulty';
  direction: 'asc' | 'desc';
}

// Server Action Response Types
export interface IServerActionResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  errors?: string[];
}

export interface IServerActionError {
  status: 'error';
  message: string;
  errors?: string[];
  statusCode?: number;
}

// ============================================================================
// VALIDATION CONSTRAINTS
// ============================================================================

export const WORKSHEET_CONSTRAINTS = {
  TITLE_MIN_LENGTH: 1,
  TITLE_MAX_LENGTH: 200,
  DESCRIPTION_MAX_LENGTH: 1000,
  QUESTION_COUNT_MIN: 1,
  QUESTION_COUNT_MAX: 100,
  BULK_OPERATION_MAX_ITEMS: 50,
} as const;

export const QUESTION_CONSTRAINTS = {
  CONTENT_MIN_LENGTH: 10,
  CONTENT_MAX_LENGTH: 2000,
  EXPLAIN_MIN_LENGTH: 10,
  EXPLAIN_MAX_LENGTH: 1000,
  OPTIONS_MIN_COUNT: 1,
  OPTIONS_MAX_COUNT: 10,
  POINTS_MIN: 0,
  POINTS_MAX: 100,
  UPDATE_REASON_MAX_LENGTH: 500,
} as const;

// ============================================================================
// TYPE GUARDS
// ============================================================================

export function isValidQuestionType(type: string): type is EQuestionType {
  return Object.values(EQuestionType).includes(type as EQuestionType);
}

export function isValidQuestionDifficulty(difficulty: string): difficulty is EQuestionDifficulty {
  return Object.values(EQuestionDifficulty).includes(difficulty as EQuestionDifficulty);
}

export function isValidSelectionStrategy(strategy: string): strategy is ESelectionStrategy {
  return Object.values(ESelectionStrategy).includes(strategy as ESelectionStrategy);
}

// ============================================================================
// TYPE ALIASES FOR BACKWARD COMPATIBILITY
// ============================================================================

export type TWorksheet = IWorksheetResponse;
export type Question = IQuestionResponse;
export type CreateWorksheetDto = ICreateWorksheetDto;
export type WorksheetGeneratingStatus = EWorksheetStatus;
