/** @type {import('next').NextConfig} */
const nextConfig: import('next').NextConfig = {
  // Enable React strict mode for better development experience
  reactStrictMode: process.env.NODE_ENV === 'development',
  env:{API_URL: process.env.NEXT_PUBLIC_API_URL},
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },

  // Configure webpack to handle ESM modules properly
  webpack: (config: any, { isServer }: { isServer: boolean }) => {
    // Add specific handling for framer-motion
    config.resolve.alias = {
      ...config.resolve.alias,
      // Force framer-motion to resolve to the same instance
      'framer-motion': require.resolve('framer-motion'),
    };

    // Handle .mjs files properly
    config.module.rules.push({
      test: /\.mjs$/,
      include: /node_modules/,
      type: 'javascript/auto',
    });

    // Prevent specific problematic modules from being processed
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
      };
    }

    return config;
  },

  // Transpile specific modules that might cause issues
  transpilePackages: ['framer-motion', '@react-pdf/renderer'],

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.daisyui.com',
        port: '',
        pathname: '/images/stock/**',
      },
      {
        protocol: 'https',
        hostname: "api-edusg.okiena.com",
        port: '',
        pathname: '/files/render/**',
      },
    ],
  },
};

module.exports = nextConfig;
