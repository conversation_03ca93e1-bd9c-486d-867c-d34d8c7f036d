import React, { Suspense } from 'react';
import Link from 'next/link';
import { handleGetSubscriptionStatusAction } from '@/actions/package.action';
import { Button } from '@/components/atoms/Button/Button';
import Container from '@/components/atoms/Container/Container';
import { <PERSON><PERSON><PERSON><PERSON>, Alert<PERSON>riangle, Loader2, PartyPopper } from 'lucide-react';
import { cn } from '@/utils/cn';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';

// Main layout for the page
function PageLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative min-h-screen w-full bg-white">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px]"></div>
      <Container className="relative z-10 flex min-h-screen items-center justify-center">
        <div className="w-full max-w-md">{children}</div>
      </Container>
    </div>
  );
}

// Reusable card component for displaying status
function StatusCard({
  icon: Icon,
  iconClassName,
  title,
  message,
  children,
}: {
  icon: React.ElementType;
  iconClassName: string;
  title: string;
  message: React.ReactNode;
  children?: React.ReactNode;
}) {
  return (
    <div className="overflow-hidden rounded-xl border border-gray-200 bg-white/80 shadow-lg backdrop-blur-sm">
      <div className="p-8 text-center">
        <div className="mb-4 flex justify-center">
          <Icon className={cn('h-16 w-16', iconClassName)} />
        </div>
        <h1 className="mb-2 text-2xl font-bold text-gray-800">{title}</h1>
        <div className="mb-6 text-gray-600">{message}</div>
        {children}
      </div>
    </div>
  );
}

// Success state component
function SuccessState({ subscription }: { subscription: any }) {
  return (
    <StatusCard
      icon={PartyPopper}
      iconClassName="text-green-500"
      title="Payment Successful!"
      message={
        <>
          <p className="mb-4">Your subscription to the <span className="font-semibold">{subscription.package.name}</span> plan is now active.</p>
          <p className="text-sm text-gray-500">
            A confirmation has been sent to your email. You now have access to all premium features.
          </p>
        </>
      }
    >
      <div className="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-center">
        <Button href="/manage-worksheet" className="w-full sm:w-auto">
          Go to Worksheets
        </Button>
        <Button href="/profile/billing" variant="outline" className="w-full sm:w-auto">
          View Billing
        </Button>
      </div>
    </StatusCard>
  );
}

// Error state component
function ErrorState({ message }: { message: string }) {
  return (
    <StatusCard
      icon={AlertTriangle}
      iconClassName="text-red-500"
      title="Payment Verification Failed"
      message={<p>{message}</p>}
    >
      <div className="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-center">
        <Button href="/pricing" className="w-full sm:w-auto">
          View Pricing Plans
        </Button>
        <Button href="/contact-support" variant="outline" className="w-full sm:w-auto">
          Contact Support
        </Button>
      </div>
    </StatusCard>
  );
}

// Loading skeleton
function LoadingState() {
  return (
    <StatusCard
      icon={Loader2}
      iconClassName="text-gray-400 animate-spin"
      title="Verifying Payment..."
      message="Please wait a moment while we confirm your subscription status. This won't take long."
    />
  );
}

// Component to handle the actual subscription check and display
async function SubscriptionStatusChecker({ sessionId }: { sessionId?: string | string[] }) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return <ErrorState message="User not authenticated." />;
  }
  // Here you could add logic to use the sessionId to verify the specific transaction if needed.
  // For now, we fetch the general subscription status as per the original implementation.
  const response = await handleGetSubscriptionStatusAction();


  if (response.status === 'error') {
    const errorMessage =
      typeof response.message === 'string'
        ? response.message
        : 'An unexpected error occurred. Please contact support.';
    return <ErrorState message={errorMessage} />;
  }

  if (response.data?.status === 'active' || response.data?.status === 'trialing') {
    return <SuccessState subscription={response.data} />;
  }

  // Fallback for cases where payment was received but subscription is not yet active
  return (
    <ErrorState message="We received your payment, but there was an issue activating your subscription. Please contact support for assistance." />
  );
}

// Main page component
const SuccessPage = async ({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) => {
  const sessionId = searchParams?.session_id;

  return (
    <PageLayout>
      <Suspense fallback={<LoadingState />}>
        <SubscriptionStatusChecker sessionId={sessionId} />
      </Suspense>
    </PageLayout>
  );
};

export default SuccessPage; 