'use client';

import { Suspense, useEffect, useState } from 'react';
import { handleGetPackagesPublicAction, handleCreateSubscriptionSessionAction, handleGetSubscriptionStatusAction } from '@/actions/package.action';
import { PricingCard, PackageData } from '@/components/molecules/PricingCard';
import { AlertTriangle } from 'lucide-react';
import { useSession } from 'next-auth/react';

// Transform IPackageResponse to PackageData for PricingCard component
function transformPackageData(packages: any[]): PackageData[] {
  return packages.map((pkg) => {
    const activePrice = pkg.prices?.find((price: any) => 
      price.active && price.type === 'recurring'
    ) || pkg.prices?.find((price: any) => price.active) || pkg.prices?.[0];

    const price = activePrice ? activePrice.unitAmount / 100 : 0;
    
    const features = [
      'Full access to platform',
      '24/7 customer support',
      'Regular updates included',
      'Secure data storage',
    ];

    return {
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      price: price,
      features: features,
      currency: activePrice?.currency?.toUpperCase() || 'SGD',
      interval: activePrice?.interval || 'month',
      popular: pkg.name.toLowerCase().includes('pro') || pkg.name.toLowerCase().includes('premium'),
      stripeProductId: pkg.stripeProductId,
    };
  });
}

// Helper function to format error messages
function formatErrorMessage(message: string | Array<{ field: string; constraints: string }>): string {
  if (typeof message === 'string') {
    return message;
  }
  if (Array.isArray(message)) {
    return message.map(error => `${error.field}: ${error.constraints}`).join(', ');
  }
  return 'An unexpected error occurred.';
}

// Loading component
function SubscriptionPageSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <div className="h-10 bg-gray-200 rounded-lg mx-auto max-w-xs mb-4 animate-pulse"></div>
        <div className="h-6 bg-gray-200 rounded-lg mx-auto max-w-md animate-pulse"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8 max-w-7xl mx-auto">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-xl border border-gray-200 p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 rounded mb-6"></div>
            <div className="h-8 bg-gray-200 rounded mb-6"></div>
            <div className="space-y-3 mb-8">
              {[1, 2, 3, 4].map((j) => (
                <div key={j} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-12 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Error component
function SubscriptionPageError({ message, onRetry }: { message: string; onRetry: () => void }) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto text-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Unable to Load Subscription Details
          </h3>
          <p className="text-red-600 text-sm mb-4">
            {message || 'There was an error loading your subscription details. Please try again later.'}
          </p>
          <button 
            onClick={onRetry} 
            className="btn btn-outline btn-error btn-sm"
          >
            Retry
          </button>
        </div>
      </div>
    </div>
  );
}

function SubscriptionManagementContent() {
  const { data: session } = useSession();
  const [packages, setPackages] = useState<PackageData[]>([]);
  const [currentPackageId, setCurrentPackageId] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscribingPackageId, setSubscribingPackageId] = useState<string | null>(null);

  const fetchData = async () => {
    if (!session?.user.accessToken) return;
    try {
      setLoading(true);
      setError(null);
      
      const [packagesResponse, subscriptionResponse] = await Promise.all([
        handleGetPackagesPublicAction(),
        handleGetSubscriptionStatusAction(session.user.accessToken)
      ]);
      
      if (packagesResponse.status === 'error') {
        setError(formatErrorMessage(packagesResponse.message));
        return;
      }
      
      if (subscriptionResponse.status === 'success' && subscriptionResponse.data) {
        setCurrentPackageId(subscriptionResponse.data.package?.id);
      } else if (subscriptionResponse.status === 'error') {
        console.warn("Could not fetch subscription status:", subscriptionResponse.message)
      }

      const transformedPackages = transformPackageData(packagesResponse.data || []);
      setPackages(transformedPackages);

    } catch (err) {
      setError('Failed to fetch subscription details. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if(session) {
      if (session.user.accessToken) {
        fetchData();
      }
    } else if (session === null) { // Not loading, no session
        setLoading(false);
        setError("You must be signed in to manage your subscription.");
    }
  }, [session]);

  const handleSubscriptionChange = async (packageData: PackageData) => {
    if (!packageData.id) {
      setError('Invalid package selected. Please try again.');
      return;
    }

    try {
      setSubscribingPackageId(packageData.id);
      setError(null);

      const currentOrigin = window.location.origin;
      
      const payload = {
        packageId: packageData.id,
        successUrl: `${currentOrigin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${currentOrigin}/subscription/me`,
      };

      const response = await handleCreateSubscriptionSessionAction(payload);

      if (response.status === 'error') {
        setError(formatErrorMessage(response.message));
        return;
      }

      if (response.data) {
        window.location.href = response.data;
      } else {
        setError('Failed to create checkout session. Please try again.');
      }
    } catch (err) {
      console.error('Subscription management error:', err);
      setError('Failed to start subscription process. Please try again.');
    } finally {
      setSubscribingPackageId(null);
    }
  };

  const handleManageCurrentSubscription = (packageData: PackageData) => {
    // This would typically redirect to a Stripe customer portal
    alert(`Managing your ${packageData.name} plan. Portal redirection not yet implemented.`);
  }

  if (loading) {
    return <SubscriptionPageSkeleton />;
  }

  if (error) {
    return <SubscriptionPageError message={error} onRetry={fetchData} />;
  }

  return (
    <div className="relative overflow-hidden">
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:45px_45px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]"></div>
      
      <div className="relative z-10 container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Manage Your Subscription</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Here is your current plan. You can upgrade, downgrade, or manage your subscription details.
          </p>
        </div>

        <div className={`grid gap-x-6 gap-y-8 max-w-7xl mx-auto ${
          packages.length === 1 
            ? 'grid-cols-1 justify-items-center max-w-md' 
            : packages.length === 2 
            ? 'grid-cols-1 md:grid-cols-2 justify-items-center max-w-4xl' 
            : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        }`}>
          {packages.map((pkg) => {
            const isCurrent = pkg.id === currentPackageId;
            return (
              <PricingCard
                key={pkg.id}
                package={pkg}
                onSubscribe={handleSubscriptionChange}
                onManageSubscription={handleManageCurrentSubscription}
                isLoading={subscribingPackageId === pkg.id}
                disabled={(subscribingPackageId !== null && subscribingPackageId !== pkg.id) || isCurrent}
                isCurrentPlan={isCurrent}
                className="h-full w-full max-w-sm"
              />
            );
          })}
        </div>
      </div>
    </div>
  );
}


export default function SubscriptionPage() {
    return (
        <Suspense fallback={<SubscriptionPageSkeleton />}>
            <SubscriptionManagementContent />
        </Suspense>
    )
}