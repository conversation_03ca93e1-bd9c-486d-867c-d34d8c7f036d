'use client';

import { Suspense, useEffect, useState } from 'react';
import { handleGetPackagesPublicAction, handleCreateSubscriptionSessionAction } from '@/actions/package.action';
import { PricingCard, PackageData } from '@/components/molecules/PricingCard';
import { <PERSON><PERSON><PERSON>riangle, Loader2 } from 'lucide-react';

// Transform IPackageResponse to PackageData for PricingCard component
function transformPackageData(packages: any[]): PackageData[] {
  return packages.map((pkg) => {
    // Find the first active price, preferring recurring prices
    const activePrice = pkg.prices?.find((price: any) => 
      price.active && price.type === 'recurring'
    ) || pkg.prices?.find((price: any) => price.active) || pkg.prices?.[0];

    // Convert unitAmount (cents) to dollars
    const price = activePrice ? activePrice.unitAmount / 100 : 0;
    
    // Extract features from description or provide defaults
    // For now, we'll create generic features since the API doesn't provide a features array
    const features = [
      'Full access to platform',
      '24/7 customer support',
      'Regular updates included',
      'Secure data storage',
    ];

    return {
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      price: price,
      features: features,
      currency: activePrice?.currency?.toUpperCase() || 'SGD',
      interval: activePrice?.interval || 'month',
      highlighted: false,
      popular: pkg.name.toLowerCase().includes('pro') || pkg.name.toLowerCase().includes('premium'),
      stripeProductId: pkg.stripeProductId,
    };
  });
}

// Helper function to format error messages
function formatErrorMessage(message: string | Array<{ field: string; constraints: string }>): string {
  if (typeof message === 'string') {
    return message;
  }
  // If it's an array of validation errors, format them nicely
  if (Array.isArray(message)) {
    return message.map(error => `${error.field}: ${error.constraints}`).join(', ');
  }
  return 'An unexpected error occurred.';
}

// Loading component
function PricingPageSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <div className="h-10 bg-gray-200 rounded-lg mx-auto max-w-xs mb-4 animate-pulse"></div>
        <div className="h-6 bg-gray-200 rounded-lg mx-auto max-w-md animate-pulse"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8 max-w-7xl mx-auto">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-xl border border-gray-200 p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 rounded mb-6"></div>
            <div className="h-8 bg-gray-200 rounded mb-6"></div>
            <div className="space-y-3 mb-8">
              {[1, 2, 3, 4].map((j) => (
                <div key={j} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-12 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Error component
function PricingPageError({ message, onRetry }: { message: string; onRetry: () => void }) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto text-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Unable to Load Pricing
          </h3>
          <p className="text-red-600 text-sm mb-4">
            {message || 'There was an error loading the pricing packages. Please try again later.'}
          </p>
          <button 
            onClick={onRetry} 
            className="btn btn-outline btn-error btn-sm"
          >
            Retry
          </button>
        </div>
      </div>
    </div>
  );
}

// Main content component - now a client component
function PricingPageContent() {
  const [packages, setPackages] = useState<PackageData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscribingPackageId, setSubscribingPackageId] = useState<string | null>(null);

  // Fetch packages on component mount
  const fetchPackages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await handleGetPackagesPublicAction();
      
      if (response.status === 'error') {
        setError(formatErrorMessage(response.message));
        return;
      }

      const transformedPackages = transformPackageData(response.data || []);
      setPackages(transformedPackages);
    } catch (err) {
      setError('Failed to fetch packages. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackages();
  }, []);

  // Handle subscription
  const handleSubscribe = async (packageData: PackageData) => {
    if (!packageData.id) {
      setError('Invalid package selected. Please try again.');
      return;
    }

    try {
      setSubscribingPackageId(packageData.id);
      setError(null);

      // Get the current URL for success/cancel redirects
      const currentOrigin = window.location.origin;
      
      // Create subscription session payload
      const payload = {
        packageId: packageData.id,
        successUrl: `${currentOrigin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${currentOrigin}/pricing`,
      };

      const response = await handleCreateSubscriptionSessionAction(payload);

      if (response.status === 'error') {
        setError(formatErrorMessage(response.message));
        return;
      }

      // Redirect to Stripe checkout
      if (response.data) {
        window.location.href = response.data;
      } else {
        setError('Failed to create checkout session. Please try again.');
      }
    } catch (err) {
      console.error('Subscription error:', err);
      setError('Failed to start subscription process. Please try again.');
    } finally {
      setSubscribingPackageId(null);
    }
  };

  // Show loading state
  if (loading) {
    return <PricingPageSkeleton />;
  }

  // Show error state
  if (error) {
    return <PricingPageError message={error} onRetry={fetchPackages} />;
  }

  // Handle empty packages
  if (packages.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Our Pricing</h1>
          <p className="text-lg text-gray-600 mb-8">Choose the perfect plan for your needs</p>
          
          <div className="max-w-md mx-auto">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <p className="text-gray-600">
                No pricing packages are currently available. Please check back later.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:45px_45px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]"></div>
      
      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Our Pricing</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose the perfect plan for your educational needs. All plans include our core features with varying levels of access and support.
          </p>
        </div>

        {/* Responsive Grid Layout - Dynamically centered based on package count */}
        <div className={`grid gap-x-6 gap-y-8 max-w-7xl mx-auto ${
          packages.length === 1 
            ? 'grid-cols-1 justify-items-center max-w-md' 
            : packages.length === 2 
            ? 'grid-cols-1 md:grid-cols-2 justify-items-center max-w-4xl' 
            : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        }`}>
          {packages.map((pkg) => (
            <PricingCard
              key={pkg.id}
              package={pkg}
              onSubscribe={handleSubscribe}
              isLoading={subscribingPackageId === pkg.id}
              disabled={subscribingPackageId !== null}
              className="h-full w-full max-w-sm" // Ensure cards have equal height and consistent width
            />
          ))}
        </div>
      </div>
    </div>
  );
}

// Main exported component
export default function PricingPage() {
  return <PricingPageContent />;
}