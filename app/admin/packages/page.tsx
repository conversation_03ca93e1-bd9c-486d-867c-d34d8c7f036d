'use client';

import React from 'react';
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { Breadcrumb } from '@/components/atoms';
import { PackageManagement } from '@/components/organisms/PackageManagement/PackageManagement';

const PackageManagementPage = () => {
  // Create the header component
  const header = (
    <div className="my-3">
      <Breadcrumb items={[
        { label: 'Home', href: '/' },
        { label: 'Admin', href: '/admin' },
        { label: 'Package Management' },
      ]} />
    </div>
  );

  return (
    <ListingTemplate 
      header={header}
      table={<PackageManagement />}
    />
  );
};

export default PackageManagementPage; 