'use client';

import React from 'react';
import Container from '@/components/atoms/Container/Container';
import { AlertTriangle, Home, RotateCcw, HelpCircle } from 'lucide-react';

export default function BillingHistoryError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  React.useEffect(() => {
    // Log the error to an error reporting service
    console.error('Billing History Error:', error);
  }, [error]);

  return (
    <Container variant="default" className="px-4 py-8">
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="max-w-lg mx-auto text-center">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 md:p-10">
            {/* Error icon */}
            <div className="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertTriangle className="w-10 h-10 text-red-500" />
            </div>

            {/* Error message */}
            <div className="space-y-4 mb-8">
              <h1 className="text-2xl md:text-3xl font-bold text-text-primary">
                Oops! Something went wrong
              </h1>
              <p className="text-text-secondary text-lg">
                We encountered an unexpected error while loading your billing history.
              </p>
              
              {/* Development error details - only show in development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 mt-6">
                  <div className="text-left">
                    <h3 className="text-sm font-semibold text-gray-800 mb-2">
                      Error Details (Development)
                    </h3>
                    <p className="text-xs text-gray-600 font-mono break-words">
                      {error.message}
                    </p>
                    {error.digest && (
                      <p className="text-xs text-gray-500 mt-2">
                        Error ID: {error.digest}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Action buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={reset}
                className="btn btn-primary gap-2 flex-1 sm:flex-none"
              >
                <RotateCcw className="w-4 h-4" />
                Try Again
              </button>
              
              <button
                onClick={() => window.location.href = '/'}
                className="btn btn-outline gap-2 flex-1 sm:flex-none"
              >
                <Home className="w-4 h-4" />
                Go Home
              </button>
            </div>

            {/* Help section */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-sm text-text-secondary mb-3">
                If this problem persists, please contact our support team.
              </p>
              <button className="btn btn-ghost btn-sm gap-2">
                <HelpCircle className="w-4 h-4" />
                Contact Support
              </button>
            </div>
          </div>

          {/* Additional help card */}
          <div className="bg-section-bg-accent rounded-xl p-6 border border-accent-bg-light mt-6">
            <div className="flex items-center justify-center gap-3">
              <div className="w-8 h-8 bg-link-default/10 rounded-lg flex items-center justify-center">
                <HelpCircle className="w-4 h-4 text-link-default" />
              </div>
              <div className="text-center">
                <h3 className="font-medium text-text-primary text-sm">Need immediate help?</h3>
                <p className="text-text-secondary text-xs mt-1">
                  Check our <button className="text-link-default hover:text-link-hover underline">status page</button> or visit our <button className="text-link-default hover:text-link-hover underline">help center</button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
} 