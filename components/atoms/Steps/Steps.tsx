import React from 'react';
import { cn } from '@/utils/cn';

export interface Step {
  label: string; // Label for the step
}

export interface StepsProps {
  steps: Step[]; // Array of steps
  currentStep: number; // Current active step (1-based index)
  className?: string; // Additional class names for the steps container
}

const Steps: React.FC<StepsProps> = ({ steps, currentStep, className }) => {
  const currentStepLabel = steps[currentStep]?.label || '';
  const totalSteps = steps.length;
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

  return (
    <>
      {/* Mobile Progress Indicator */}
      <div className="block md:hidden">
        <div className="space-y-3">
          {/* Step Counter and Current Step */}
          <div className="flex justify-between items-center">
            <div>
              <div className="text-sm text-gray-600">
                Step {currentStep + 1} of {totalSteps}
              </div>
              <div className="text-lg font-semibold text-gray-900">
                {currentStepLabel}
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Desktop Steps */}
      <div className="hidden md:block">
        <ul className={cn('steps', className)}>
          {steps.map((step, index) => {
            const isCompleted = index <= currentStep; // Steps before the current step are completed
            const isActive = index === currentStep; // Current step is active

            return (
              <li
                key={`step-${index}-${step.label}`}
                className={cn(
                  'step',
                  isCompleted && 'step-primary',
                  isActive && 'step-active' // Active step
                )}
              >
                <span className="text-secondary-content text-[1rem] font-normal">
                  {step.label}
                </span>
              </li>
            );
          })}
        </ul>
      </div>
    </>
  );
};

export default Steps;
