'use client';

import * as React from 'react';
import { cn } from '@/utils/cn';
import { cva, type VariantProps } from 'class-variance-authority';

export interface TagProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof tagVariants> {
  children: React.ReactNode;
}

const tagVariants = cva(
  'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full',
  {
    variants: {
      variant: {
        default: 'bg-section-bg-neutral-alt text-text-primary',
        primary: 'bg-accent-bg-light text-link-default',
        secondary: 'bg-section-bg-neutral-alt text-text-secondary',
        success: 'bg-green-100 text-green-800',
        warning: 'bg-yellow-100 text-yellow-800',
        error: 'bg-red-100 text-red-800',
        info: 'bg-section-bg-accent text-link-default',
        pill: 'bg-tag-bg text-tag-text',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

export const Tag: React.FC<TagProps> = ({
  className,
  variant,
  children,
  ...props
}) => {
  return (
    <span
      className={cn(tagVariants({ variant }), className)}
      {...props}
    >
      {children}
    </span>
  );
};
