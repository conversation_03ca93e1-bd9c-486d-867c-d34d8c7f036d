'use client';

import React from 'react';
import { EUserRole } from '@/config/enums/user';

export interface RoleBadgeProps {
  role: EUserRole;
  className?: string;
}

export const RoleBadge: React.FC<RoleBadgeProps> = ({ role, className = '' }) => {
  const getRoleStyles = () => {
    switch (role) {
      case EUserRole.ADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case EUserRole.TEACHER:
        return 'bg-accent-bg-light text-link-default border-blue-200';
      case EUserRole.SCHOOL_MANAGER:
        return 'bg-green-100 text-green-800 border-green-200';
      case EUserRole.STUDENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-section-bg-neutral-alt text-text-primary border-gray-200';
    }
  };

  return (
    <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium border ${getRoleStyles()} ${className}`}>
      {role}
    </span>
  );
};