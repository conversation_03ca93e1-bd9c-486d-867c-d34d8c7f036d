'use client';

import * as React from 'react';
import { cn } from '@/utils/cn';

export interface PricingToggleProps {
  onToggle: (isYearly: boolean) => void;
}

export const PricingToggle: React.FC<PricingToggleProps> = ({ onToggle }) => {
  const [isYearly, setIsYearly] = React.useState(false);

  const handleToggle = () => {
    const newIsYearly = !isYearly;
    setIsYearly(newIsYearly);
    onToggle(newIsYearly);
  };

  return (
    <div className="relative flex w-full max-w-xs items-center rounded-full bg-gray-200 p-1.5">
      <button
        className="z-20 w-full rounded-full p-1.5 font-semibold text-gray-800"
        onClick={() => {
          if (isYearly) handleToggle();
        }}
      >
        Monthly
      </button>
      <button
        className="z-20 w-full rounded-full p-1.5 font-semibold text-gray-800"
        onClick={() => {
          if (!isYearly) handleToggle();
        }}
      >
        Yearly
      </button>
      <div
        className="absolute inset-0 z-10 flex w-1/2 items-center justify-center p-1.5"
        style={{
          transform: `translateX(${isYearly ? '100%' : '0'})`,
          transition: 'transform 0.3s',
        }}
      >
        <div className="h-full w-full rounded-full bg-white shadow-sm"></div>
      </div>
    </div>
  );
};