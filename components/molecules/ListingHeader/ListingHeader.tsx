import { Button, ButtonProps } from '@/components/atoms/Button/Button';

type IListingButton = ButtonProps & { label: string };
export interface IListingHeaderProps {
  title: string;
  subtitle?: string;
  buttonProps?: IListingButton | IListingButton[];
}

export const ListingHeader: React.FC<IListingHeaderProps> = ({
  title,
  subtitle,
  buttonProps,
}) => {
  const renderButton = (button?: IListingButton, index?: number) => {
    if (!button) return;
    const { label, ...restButtonProps } = button;
    return (
      <Button
        key={index}
        className="shadow-sm hover:shadow-md text-white transition-shadow px-3 py-1.5 rounded-lg flex items-center gap-1.5 w-auto text-sm"
        {...restButtonProps}
      >
        {label}
      </Button>
    );
  };
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center w-full gap-2 py-1 mb-3 border-b border-gray-100">
      <div>
        <h1 className="font-bold text-gray-800 text-xl">{title}</h1>
        {subtitle && <p className="text-gray-500 text-sm mt-0.5">{subtitle}</p>}
      </div>
      <div className="flex items-center gap-2">
        {buttonProps && Array.isArray(buttonProps)
          ? buttonProps.map(renderButton)
          : renderButton(buttonProps)}
      </div>
    </div>
  );
};
