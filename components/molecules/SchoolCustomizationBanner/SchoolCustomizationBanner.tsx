'use client';

import React, { useState } from 'react';
import { X, Setting<PERSON>, ArrowRight, Sparkles } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';
import { SchoolCustomizationAssessment, SchoolCustomizationLevel, getNextCustomizationStep } from '@/utils/schoolCustomization';

interface SchoolCustomizationBannerProps {
  assessment: SchoolCustomizationAssessment;
  schoolName: string;
  className?: string;
  dismissible?: boolean;
  onCustomizeClick?: () => void;
  onDismiss?: () => void;
}

export const SchoolCustomizationBanner: React.FC<SchoolCustomizationBannerProps> = ({
  assessment,
  schoolName,
  className,
  dismissible = true,
  onCustomizeClick,
  onDismiss
}) => {
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't show banner for fully customized schools or if dismissed
  if (assessment.level === SchoolCustomizationLevel.FULLY_CUSTOMIZED || isDismissed) {
    return null;
  }

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  const nextStep = getNextCustomizationStep(assessment);
  const isBasic = assessment.level === SchoolCustomizationLevel.BASIC;

  const getBannerContent = () => {
    if (isBasic) {
      return {
        icon: Sparkles,
        message: `${schoolName} is ready for your personal touch!`,
        action: nextStep || 'Start customizing your school',
        variant: 'info' as const
      };
    } else {
      return {
        icon: Settings,
        message: `Almost there! ${schoolName} is ${assessment.score}% customized.`,
        action: nextStep || 'Complete your school setup',
        variant: 'warning' as const
      };
    }
  };

  const content = getBannerContent();

  return (
    <div 
      role="alert" 
      className={cn(
        'alert alert-vertical sm:alert-horizontal shadow-sm border',
        content.variant === 'info' && 'alert-info border-info/20',
        content.variant === 'warning' && 'alert-warning border-warning/20',
        className
      )}
    >
      {/* Icon */}
      <content.icon className="h-5 w-5 shrink-0" />
      
      {/* Content */}
      <div className="flex-1">
        <div className="font-medium text-sm">
          {content.message}
        </div>
        <div className="text-xs opacity-80 mt-1">
          {content.action}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={onCustomizeClick}
          href="/school-customization"
          className="btn-sm gap-1"
          iconProps={{
            variant: 'arrow-right'
          }}
        >
          Customize
        </Button>
        
        {dismissible && (
          <button
            className="btn btn-ghost btn-sm btn-square"
            onClick={handleDismiss}
            aria-label="Dismiss banner"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default SchoolCustomizationBanner;
