'use client';

import React from 'react';
import { Button } from '@/components/atoms/Button/Button';
import { Trash2, XCircle } from 'lucide-react'; // Using XCircle for clear selection

export interface SchoolTableBulkActionsProps {
  selectedCount: number;
  onClearSelection: () => void;
  onBulkDelete: () => void;
  // Add other bulk actions if needed, e.g., onBulkActivate, onBulkDeactivate
  entityName?: string; // e.g., "school" or "schools"
}

export const SchoolTableBulkActions: React.FC<SchoolTableBulkActionsProps> = ({
  selectedCount,
  onClearSelection,
  onBulkDelete,
  entityName = "schools",
}) => {
  if (selectedCount === 0) {
    return null; // Don't render if nothing is selected
  }

  return (
    <div className="p-3 border-b border-gray-200 bg-blue-50">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-3">
        <p className="text-sm font-medium text-blue-700">
          {selectedCount} {selectedCount === 1 ? entityName.slice(0, -1) : entityName} selected
        </p>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="ghost"
            onClick={onClearSelection}
            className="text-sm text-blue-600 hover:bg-blue-100 px-3 py-1.5"
          >
            <XCircle size={16} className="mr-1.5" />
            Clear Selection
          </Button>
          <Button
            variant="error" // Changed "destructive" to "error" as per available Button variants
            onClick={onBulkDelete}
            className="text-sm px-3 py-1.5"
          >
            <Trash2 size={16} className="mr-1.5" />
            Delete Selected
          </Button>
        </div>
      </div>
    </div>
  );
};
