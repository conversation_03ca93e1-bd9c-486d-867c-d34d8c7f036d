'use client';

import React, { useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  RowSelectionState,
  Updater,
  useReactTable,
  PaginationState,
  ColumnDef,
} from '@tanstack/react-table';
import { cn } from '@/utils/cn';
import { TableSkeleton } from './TableSkeleton';
import { ChevronDown, ChevronRight, MoreHorizontal } from 'lucide-react';

interface MobileOptimizedTableProps<T> {
  columns: ColumnDef<T, any>[];
  tableData: T[];
  isLoading?: boolean;
  wrapperClass?: string;
  theadClass?: string;
  tbodyClass?: string;
  tableHeight?: string;
  handleClickTableRow?: (row: any) => void;
  manualPagination?: boolean;
  pageCount?: number;
  pagination?: PaginationState;
  onPaginationChange?: (updater: Updater<PaginationState>) => void;
  // Mobile-specific props
  mobileBreakpoint?: 'sm' | 'md' | 'lg';
  priorityColumns?: string[]; // Column keys to show on mobile (not used in simplified layout)
  mobileCardClassName?: string;
}

export { MobileOptimizedTable };

export default function MobileOptimizedTable<T>({
  columns,
  tableData,
  isLoading = false,
  wrapperClass = '',
  theadClass = '',
  tbodyClass = '',
  tableHeight = 'h-full',
  handleClickTableRow,
  manualPagination = true,
  pageCount = -1,
  pagination,
  onPaginationChange,
  mobileBreakpoint = 'md',
  priorityColumns = [],
  mobileCardClassName = '',
}: MobileOptimizedTableProps<T>) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [_pagination, _setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  // Removed expandedRows state as we're simplifying mobile cards

  const effectivePagination = pagination !== undefined ? pagination : _pagination;
  const effectiveSetPagination = onPaginationChange !== undefined ? onPaginationChange : _setPagination;

  const handleRowSelection = (rowSelectFnc: Updater<RowSelectionState>) => {
    if (typeof rowSelectFnc !== 'function') return;
    const newSortState = rowSelectFnc(rowSelection);
    setRowSelection(newSortState);
  };

  const table = useReactTable({
    data: tableData,
    columns: columns,
    state: {
      rowSelection,
      pagination: effectivePagination,
    },
    enableColumnResizing: true,
    manualSorting: true,
    manualPagination: manualPagination,
    columnResizeMode: 'onChange',
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: manualPagination ? undefined : getPaginationRowModel(),
    enableRowSelection: true,
    onRowSelectionChange: handleRowSelection,
    onPaginationChange: manualPagination ? effectiveSetPagination : undefined,
    ...(manualPagination && pageCount !== -1 && { pageCount: pageCount }),
    getRowId: (row: any) => String(row?.id || Math.random()),
  });

  // Removed toggleRowExpansion as we're simplifying mobile cards

  // For simplified mobile cards, we don't need priority/secondary column filtering
  // All columns are available for the custom mobile layout

  const tableRows = table?.getRowModel().rows;
  const isNodata = !isLoading && !tableRows?.length;

  const renderMobileCard = (row: any) => {
    const rowId = row.id;

    // Get specific cells for the simplified layout
    const selectCell = row.getVisibleCells().find((c: any) => c.column.id === 'select');
    const titleCell = row.getVisibleCells().find((c: any) => c.column.id === 'title');
    const createdCell = row.getVisibleCells().find((c: any) => c.column.id === 'createdAt');
    const statusCell = row.getVisibleCells().find((c: any) => c.column.id === 'status');
    const actionsCell = row.getVisibleCells().find((c: any) => c.column.id === 'actions');

    return (
      <div
        key={rowId}
        className={cn(
          'bg-white border border-gray-200 rounded-xl p-4 shadow-sm hover:shadow-lg transition-all duration-200',
          mobileCardClassName,
          handleClickTableRow && 'cursor-pointer active:scale-[0.98]'
        )}
        onClick={() => handleClickTableRow?.(row)}
      >
        {/* Top row: Checkbox + Title + Actions */}
        <div className="flex items-start gap-3 mb-3">
          {/* Checkbox on top left */}
          {selectCell && (
            <div onClick={(e) => e.stopPropagation()}>
              {flexRender(selectCell.column.columnDef.cell, selectCell.getContext())}
            </div>
          )}

          {/* Title content - takes up remaining space */}
          <div className="flex-1 min-w-0">
            {titleCell && (
              <div className="text-sm text-gray-900">
                {flexRender(titleCell.column.columnDef.cell, titleCell.getContext())}
              </div>
            )}
          </div>

          {/* Actions on top right */}
          {actionsCell && (
            <div onClick={(e) => e.stopPropagation()}>
              {flexRender(actionsCell.column.columnDef.cell, actionsCell.getContext())}
            </div>
          )}
        </div>

        {/* Bottom row: Created date + Status */}
        <div className="flex items-center justify-between">
          {/* Created date */}
          {createdCell && (
            <div className="text-sm text-gray-600">
              {flexRender(createdCell.column.columnDef.cell, createdCell.getContext())}
            </div>
          )}

          {/* Status */}
          {statusCell && (
            <div>
              {flexRender(statusCell.column.columnDef.cell, statusCell.getContext())}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderDesktopTable = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className={cn(
        'scrollbar overflow-y-auto',
        wrapperClass,
        {
          'overflow-y-hidden': isNodata || isLoading,
          [tableHeight]: !(isNodata || isLoading),
        }
      )}>
        <table className="table table-hover w-full">
          <thead className={cn('sticky top-0 z-10 overflow-y-hidden bg-gray-50', theadClass)}>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th key={header.id} className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b border-gray-200">
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className={cn('z-1 sticky top-10 overflow-y-hidden bg-white', tbodyClass)}>
            {!isLoading &&
              tableRows?.map((row) => (
                <tr
                  key={row.id}
                  className="hover:bg-gray-50 transition-colors cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => handleClickTableRow?.(row)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="px-4 py-3 text-sm text-gray-900">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <TableSkeleton
        rows={5}
        columns={columns.length}
        mobileBreakpoint={mobileBreakpoint}
        showMobileCards={true}
      />
    );
  }

  if (isNodata) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No data available</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Mobile view - Card layout */}
      <div className={cn(`block ${mobileBreakpoint}:hidden`)}>
        <div className="space-y-3">
          {tableRows?.map((row) => renderMobileCard(row))}
        </div>
      </div>

      {/* Desktop view - Table layout */}
      <div className={cn(`hidden ${mobileBreakpoint}:block`)}>
        {renderDesktopTable()}
      </div>
    </div>
  );
}
