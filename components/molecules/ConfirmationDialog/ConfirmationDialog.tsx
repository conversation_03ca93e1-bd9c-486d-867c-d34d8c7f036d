'use client';

import React from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/atoms/Dialog/Dialog';
import { AlertCircle, Loader2, X } from 'lucide-react';

export interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description?: string;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmVariant?: 'danger' | 'primary';
  isLoading?: boolean;
  children?: React.ReactNode;
  icon?: React.ReactNode;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  confirmVariant = 'primary',
  isLoading = false,
  children,
  icon
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        {/* Header with close button */}
        <DialogHeader className="relative">
          <div className="flex items-center gap-3">
            {icon && (
              <div className={`p-2 rounded-full ${confirmVariant === 'danger' ? 'bg-red-100' : 'bg-blue-100'}`}>
                {icon}
              </div>
            )}
            <DialogTitle className="flex-1">{title}</DialogTitle>
            <button
              onClick={onClose}
              disabled={isLoading}
              className="rounded-full p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200 disabled:opacity-50"
              aria-label="Close"
            >
              <X size={16} />
            </button>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="px-4 pb-4">
          {description && (
            <p className="text-gray-600 text-sm mb-4">{description}</p>
          )}
          
          {children && (
            <div className="mb-4">
              {children}
            </div>
          )}

          {/* Warning message for dangerous actions */}
          {confirmVariant === 'danger' && (
            <div className="mb-4 p-3 bg-amber-50 border-l-4 border-amber-400 rounded-r-md flex items-center gap-2">
              <AlertCircle size={16} className="text-amber-600" />
              <span className="text-amber-700 text-sm">This action cannot be undone.</span>
            </div>
          )}
        </div>

        {/* Footer with action buttons */}
        <DialogFooter className="flex justify-end gap-3">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200 disabled:opacity-50 text-sm"
          >
            {cancelLabel}
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className={`px-4 py-2 rounded-lg text-white text-sm transition-all duration-200 flex items-center gap-2 disabled:opacity-50 ${
              confirmVariant === 'danger'
                ? 'bg-red-600 hover:bg-red-700 shadow-sm hover:shadow-md'
                : 'bg-blue-600 hover:bg-blue-700 shadow-sm hover:shadow-md'
            }`}
          >
            {isLoading ? (
              <>
                <Loader2 size={14} className="animate-spin" />
                Loading...
              </>
            ) : (
              confirmLabel
            )}
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 