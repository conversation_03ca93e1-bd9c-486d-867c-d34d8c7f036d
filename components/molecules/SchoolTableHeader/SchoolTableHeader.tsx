'use client';

import React from 'react';
import { Input } from '@/components/atoms/Input/Input';
import { Button } from '@/components/atoms/Button/Button';
import { Search, X, Filter, Download, ChevronDown, ChevronUp, PlusCircle } from 'lucide-react'; // Added PlusCircle for create button

export interface SchoolTableHeaderProps {
  title: string;
  subtitle: string;
  searchTerm: string;
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClearSearch: () => void;
  onToggleFilterPanel?: () => void; // Optional: if filters are added
  isFilterPanelOpen?: boolean; // Optional
  hasActiveFilters?: boolean; // Optional
  activeFiltersCount?: number; // Optional
  onExport?: () => void; // Optional: if export functionality is added
  onCreate?: () => void; // For "Create New School" button
  createButtonLabel?: string;
}

export const SchoolTableHeader: React.FC<SchoolTableHeaderProps> = ({
  title,
  subtitle,
  searchTerm,
  onSearchChange,
  onClearSearch,
  onToggleFilterPanel,
  isFilterPanelOpen,
  hasActiveFilters,
  activeFiltersCount = 0,
  onExport,
  onCreate,
  createButtonLabel = "Create New School",
}) => {
  return (
    <div className="p-4 md:p-6 border-b border-gray-200 bg-white rounded-t-xl">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        {/* Left side: Title and Subtitle */}
        <div className="flex-1">
          <h2 className="text-xl md:text-2xl font-semibold text-gray-800">{title}</h2>
          <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
        </div>

        {/* Right side: Actions (Search, Filter, Export, Create) */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
          {/* Search Input */}
          <div className="relative w-full sm:w-64">
            <Search
              size={18}
              className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none z-10"
            />
            <Input
              type="text"
              placeholder="Search schools..."
              value={searchTerm}
              onChange={onSearchChange}
              hasLeftIcon={true}
              hasRightIcon={!!searchTerm}
              className="w-full text-sm"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0 z-10"
                onClick={onClearSearch}
                aria-label="Clear search"
              >
                <X size={16} className="text-gray-500" />
              </Button>
            )}
          </div>

          {/* Filter Button (Optional) */}
          {onToggleFilterPanel && (
            <Button
              variant="outline"
              onClick={onToggleFilterPanel}
              className="relative w-full sm:w-auto justify-start sm:justify-center text-sm"
              aria-expanded={isFilterPanelOpen} // Added aria-expanded for accessibility
            >
              <Filter size={16} className="mr-2" />
              Filter
              {hasActiveFilters && activeFiltersCount > 0 && (
                <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full">
                  {activeFiltersCount}
                </span>
              )}
              {isFilterPanelOpen ? <ChevronUp size={16} className="ml-2" /> : <ChevronDown size={16} className="ml-2" />}
            </Button>
          )}

          {/* Export Button (Optional) */}
          {onExport && (
            <Button 
              variant="outline" 
              onClick={onExport} 
              className="w-full sm:w-auto justify-start sm:justify-center text-sm"
            >
              <Download size={16} className="mr-2" />
              Export
            </Button>
          )}
          
          {/* Create New School Button */}
          {onCreate && (
            <Button
              onClick={onCreate}
              className="w-full sm:w-auto justify-start sm:justify-center text-sm bg-blue-600 hover:bg-blue-700 text-white"
            >
              <PlusCircle size={16} className="mr-2" />
              {createButtonLabel}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
