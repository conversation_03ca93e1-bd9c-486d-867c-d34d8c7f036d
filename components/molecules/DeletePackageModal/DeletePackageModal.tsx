'use client';

import React, { useState } from 'react';
import { ConfirmationDialog } from '@/components/molecules/ConfirmationDialog';
import { handleDeletePackageAction } from '@/actions/package.action';
import { IPackageResponse } from '@/apis/packageApi';
import { Trash, Package, AlertCircle } from 'lucide-react';

export interface DeletePackageModalProps {
  isOpen: boolean;
  onClose: () => void;
  package: Pick<IPackageResponse, 'id' | 'name' | 'description' | 'prices' | 'deletedAt'>;
  onSuccess?: () => void;
}

export const DeletePackageModal: React.FC<DeletePackageModalProps> = ({
  isOpen,
  onClose,
  package: packageData,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleDelete = async () => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await handleDeletePackageAction(packageData.id);

      if (response.status === 'success') {
        setSuccess('Package deleted successfully!');
        // Call onSuccess callback after a short delay
        setTimeout(() => {
          if (onSuccess) {
            onSuccess();
          }
          onClose();
          // Reset states for next time
          setSuccess(null);
          setError(null);
        }, 2000);
      } else {
        setError(Array.isArray(response.message) ? response.message.join(', ') : response.message);
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred while deleting the package.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setError(null);
      setSuccess(null);
      onClose();
    }
  };

  const formatPrice = (unitAmount: number, interval?: string, currency: string = 'USD') => {
    const priceInDollars = unitAmount / 100; // Convert from cents to dollars
    const formattedPrice = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(priceInDollars);
    
    if (!interval) return formattedPrice;
    
    const intervalMap = {
      'day': 'day',
      'week': 'week', 
      'month': 'mo',
      'year': 'yr'
    };
    
    return `${formattedPrice}/${intervalMap[interval as keyof typeof intervalMap] || interval}`;
  };

  const getStatus = (deletedAt: string | null) => {
    return deletedAt ? 'inactive' : 'active';
  };

  const firstPrice = packageData.prices?.[0];

  return (
    <ConfirmationDialog
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={handleDelete}
      title="Delete Package"
      description="Are you sure you want to delete this package? This action cannot be undone."
      confirmLabel={isSubmitting ? 'Deleting...' : 'Delete Package'}
      cancelLabel="Cancel"
      confirmVariant="danger"
      isLoading={isSubmitting}
      icon={<Trash className="text-red-600" size={20} />}
    >
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
          <AlertCircle className="text-red-500 mr-2" size={18} />
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center">
          <div className="text-green-500 mr-2">✓</div>
          <p className="text-green-600 text-sm">{success}</p>
        </div>
      )}

      {/* Package details card */}
      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 flex items-start gap-3">
        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 shadow-sm flex-shrink-0">
          <Package size={20} />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-800 mb-1">{packageData.name}</h3>
          {packageData.description && (
            <p className="text-xs text-gray-600 mb-2 line-clamp-2">{packageData.description}</p>
          )}
          <div className="flex items-center gap-3 text-xs">
            {firstPrice ? (
              <span className="font-medium text-gray-900">
                {formatPrice(firstPrice.unitAmount, firstPrice.interval, firstPrice.currency)}
              </span>
            ) : (
              <span className="text-gray-500">No pricing</span>
            )}
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
              getStatus(packageData.deletedAt) === 'active' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {getStatus(packageData.deletedAt)}
            </span>
          </div>
        </div>
      </div>
    </ConfirmationDialog>
  );
}; 