import { Input } from '@/components/atoms/Input/Input';
import { cn } from '@/utils/cn';
import { Eye, EyeOff } from 'lucide-react';
import React, { useState, useEffect } from 'react';

interface PasswordStrengthProps {
  password: string;
}

const PasswordStrength: React.FC<PasswordStrengthProps> = ({ password }) => {
  const [strength, setStrength] = useState(0);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (!password) {
      setStrength(0);
      setMessage('');
      return;
    }

    // Calculate password strength
    let score = 0;
    
    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    
    // Complexity checks
    if (/[A-Z]/.test(password)) score += 1; // Has uppercase
    if (/[a-z]/.test(password)) score += 1; // Has lowercase
    if (/[0-9]/.test(password)) score += 1; // Has number
    if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special char
    
    // Set strength level (0-4)
    let strengthLevel = 0;
    if (score >= 2) strengthLevel = 1; // Weak
    if (score >= 4) strengthLevel = 2; // Medium
    if (score >= 6) strengthLevel = 3; // Strong
    
    setStrength(strengthLevel);
    
    // Set appropriate message
    switch (strengthLevel) {
      case 0:
        setMessage('Too weak');
        break;
      case 1:
        setMessage('Weak');
        break;
      case 2:
        setMessage('Medium');
        break;
      case 3:
        setMessage('Strong');
        break;
      default:
        setMessage('');
    }
  }, [password]);

  if (!password) return null;

  return (
    <div className="mt-2">
      <div className="flex items-center gap-2 mb-1">
        <div className="flex-1 h-1.5 bg-section-bg-neutral-alt rounded-full overflow-hidden">
          <div 
            className={cn(
              'h-full rounded-full transition-all duration-300',
              {
                'w-1/4 bg-red-500': strength === 0,
                'w-2/4 bg-yellow-500': strength === 1,
                'w-3/4 bg-link-default': strength === 2,
                'w-full bg-green-500': strength === 3,
              }
            )}
          />
        </div>
        <span 
          className={cn(
            'text-xs font-medium',
            {
              'text-red-600': strength === 0,
              'text-yellow-600': strength === 1,
              'text-link-default': strength === 2,
              'text-green-600': strength === 3,
            }
          )}
        >
          {message}
        </span>
      </div>
    </div>
  );
};

const PasswordInput = React.forwardRef<
  HTMLInputElement,
  React.ComponentPropsWithRef<typeof Input> & {
    showStrengthMeter?: boolean;
    hasLeftIcon?: boolean;
  }
>(({ className, showStrengthMeter = false, hasLeftIcon = false, ...props }, ref) => {
  const [showPassword, setShowPassword] = useState(false);
  const [value, setValue] = useState('');

  return (
    <div>
      <div className="relative">
        <Input
          type={showPassword ? 'text' : 'password'}
          hasLeftIcon={hasLeftIcon}
          hasRightIcon={true}
          className={className}
          ref={ref}
          onChange={(e) => {
            setValue(e.target.value);
            if (props.onChange) props.onChange(e);
          }}
          {...props}
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          title={showPassword ? 'Hide password' : 'Show password'}
          className="absolute right-3 top-1/2 -translate-y-1/2 transform text-text-secondary hover:text-text-primary focus:outline-none focus:text-text-primary z-10"
        >
          {showPassword ? (
            <EyeOff className="size-5" />
          ) : (
            <Eye className="size-5" />
          )}
        </button>
      </div>
      {showStrengthMeter && <PasswordStrength password={value} />}
    </div>
  );
});

PasswordInput.displayName = 'PasswordInput';

export { PasswordInput };
