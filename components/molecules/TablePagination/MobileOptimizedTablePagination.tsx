'use client';

import React from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface MobileOptimizedTablePaginationProps {
  currentPage: number;
  totalPages: number;
  rowsPerPage: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onRowsPerPageChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
}

export const MobileOptimizedTablePagination: React.FC<MobileOptimizedTablePaginationProps> = ({
  currentPage,
  totalPages,
  rowsPerPage,
  totalItems,
  onPageChange,
  onRowsPerPageChange
}) => {
  // Generate page numbers for mobile (simplified)
  const getMobilePageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 3; // Show fewer pages on mobile
    
    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      // Show current page and neighbors if not at start/end
      if (currentPage > 2 && currentPage < totalPages - 1) {
        if (currentPage > 3) pages.push('...');
        pages.push(currentPage);
        if (currentPage < totalPages - 2) pages.push('...');
      } else if (currentPage <= 2) {
        // Near the beginning
        pages.push(2);
        if (totalPages > 3) pages.push('...');
      } else {
        // Near the end
        pages.push('...');
        pages.push(totalPages - 1);
      }
      
      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  // Generate page numbers for desktop (full)
  const getDesktopPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 7;
    
    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      
      let start = Math.max(2, currentPage - 2);
      let end = Math.min(totalPages - 1, currentPage + 2);
      
      if (currentPage <= 3) {
        end = Math.min(totalPages - 1, 5);
      } else if (currentPage >= totalPages - 2) {
        start = Math.max(2, totalPages - 4);
      }
      
      if (start > 2) {
        pages.push('...');
      }
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      if (end < totalPages - 1) {
        pages.push('...');
      }
      
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const renderMobilePagination = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 space-y-4">
      {/* Results info - Hide when showing "1-1 of 1" on mobile */}
      {totalItems > 1 && (
        <div className="text-center">
          <span className="text-sm text-gray-600">
            <span className="font-medium">{Math.min((currentPage - 1) * rowsPerPage + 1, totalItems)}</span>-
            <span className="font-medium">{Math.min(currentPage * rowsPerPage, totalItems)}</span> of{' '}
            <span className="font-medium">{totalItems}</span>
          </span>
        </div>
      )}

      {/* Page navigation */}
      <div className="flex items-center justify-between">
        {/* Previous button - Icon only */}
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={cn(
            'flex items-center justify-center w-10 h-10 rounded-lg text-sm font-medium transition-colors',
            currentPage === 1
              ? 'text-gray-300 cursor-not-allowed bg-gray-50'
              : 'text-gray-700 hover:bg-gray-100 bg-gray-50'
          )}
          aria-label="Previous page"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>

        {/* Page numbers - simplified for mobile */}
        <div className="flex items-center gap-1">
          {getMobilePageNumbers().map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <span className="px-2 py-1 text-gray-400">
                  <MoreHorizontal className="w-4 h-4" />
                </span>
              ) : (
                <button
                  onClick={() => onPageChange(page as number)}
                  className={cn(
                    'w-10 h-10 rounded-lg text-sm font-medium transition-colors',
                    currentPage === page
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  )}
                >
                  {page}
                </button>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Next button - Icon only */}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={cn(
            'flex items-center justify-center w-10 h-10 rounded-lg text-sm font-medium transition-colors',
            currentPage === totalPages
              ? 'text-gray-300 cursor-not-allowed bg-gray-50'
              : 'text-gray-700 hover:bg-gray-100 bg-gray-50'
          )}
          aria-label="Next page"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );

  const renderDesktopPagination = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 px-6 py-4">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
        {/* Results info and rows per page */}
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-700">
            Showing <span className="font-medium">{Math.min((currentPage - 1) * rowsPerPage + 1, totalItems)}</span> to{' '}
            <span className="font-medium">{Math.min(currentPage * rowsPerPage, totalItems)}</span> of{' '}
            <span className="font-medium">{totalItems}</span> results
          </span>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Show:</span>
            <select
              value={rowsPerPage}
              onChange={onRowsPerPageChange}
              className="px-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>
        
        {/* Page navigation */}
        <nav className="flex items-center gap-1" aria-label="Pagination">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={cn(
              'flex items-center justify-center w-10 h-10 rounded-lg border text-sm font-medium transition-colors',
              currentPage === 1
                ? 'text-gray-300 cursor-not-allowed border-gray-200 bg-gray-50'
                : 'text-gray-500 hover:bg-gray-50 border-gray-300 bg-white'
            )}
            aria-label="Previous page"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          
          {getDesktopPageNumbers().map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <span className="flex items-center justify-center w-10 h-10 text-gray-400">
                  <MoreHorizontal className="w-4 h-4" />
                </span>
              ) : (
                <button
                  onClick={() => onPageChange(page as number)}
                  className={cn(
                    'flex items-center justify-center w-10 h-10 rounded-lg text-sm font-medium transition-colors',
                    currentPage === page
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-700 hover:bg-gray-50 border border-gray-300 bg-white'
                  )}
                  aria-current={currentPage === page ? 'page' : undefined}
                  aria-label={`Page ${page}`}
                >
                  {page}
                </button>
              )}
            </React.Fragment>
          ))}
          
          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={cn(
              'flex items-center justify-center w-10 h-10 rounded-lg border text-sm font-medium transition-colors',
              currentPage === totalPages
                ? 'text-gray-300 cursor-not-allowed border-gray-200 bg-gray-50'
                : 'text-gray-500 hover:bg-gray-50 border-gray-300 bg-white'
            )}
            aria-label="Next page"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </nav>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile pagination - Only show when more than 2 pages */}
      {totalPages > 2 && (
        <div className="block md:hidden">
          {renderMobilePagination()}
        </div>
      )}

      {/* Desktop pagination - Only show when more than 2 pages */}
      {totalPages > 2 && (
        <div className="hidden md:block">
          {renderDesktopPagination()}
        </div>
      )}
    </>
  );
};
