'use client';

import * as React from 'react';
import { Check, ArrowRight } from 'lucide-react';
import { cn } from '@/utils/cn';
import { Button } from '@/components/atoms/Button/Button';

// Package interface that aligns with the API structure
export interface PackageData {
  id?: string;
  name: string;
  description: string;
  price: number | string;
  features: string[];
  currency?: string;
  interval?: 'month' | 'year' | 'day' | 'week';
  highlighted?: boolean;
  popular?: boolean;
  stripeProductId?: string;
}

export interface PricingCardProps {
  package: PackageData;
  onSubscribe?: (packageData: PackageData) => void;
  onManageSubscription?: (packageData: PackageData) => void;
  className?: string;
  isLoading?: boolean;
  disabled?: boolean;
  isCurrentPlan?: boolean;
}

export const PricingCard: React.FC<PricingCardProps> = ({
  package: packageData,
  onSubscribe,
  onManageSubscription,
  className,
  isLoading = false,
  disabled = false,
  isCurrentPlan = false,
}) => {
  const { name, description, price, features, currency = 'USD', interval = 'month', popular = false } = packageData;

  // Format price display
  const formatPrice = (price: number | string): string => {
    if (typeof price === 'string') return price;
    if (typeof price === 'number') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(price);
    }
    return '$0';
  };

  const handleAction = () => {
    if (disabled || isLoading) return;
    if (isCurrentPlan && onManageSubscription) {
      onManageSubscription(packageData);
    } else if (onSubscribe) {
      onSubscribe(packageData);
    }
  };

  return (
    <div
      className={cn(
        'relative flex flex-col h-full w-full bg-background-default rounded-xl md:rounded-2xl border transition-all duration-300',
        // Responsive hover effects
        'hover:shadow-md md:hover:shadow-lg lg:hover:shadow-xl',
        {
          // Popular card styling
          'border-link-default/30 shadow-md md:shadow-lg lg:shadow-xl ring-1 ring-link-default/20': popular && !isCurrentPlan,
          'border-green-500 ring-2 ring-green-500/50': isCurrentPlan,
          'border-gray-200 hover:border-gray-300': !popular && !isCurrentPlan,
        },
        className
      )}
      role="article"
      aria-labelledby={`pricing-card-${name.toLowerCase().replace(/\s+/g, '-')}`}
    >
      {/* Popular Badge */}
      {popular && !isCurrentPlan && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-20">
          <div className="bg-link-default text-background-default text-xs font-semibold px-3 py-1.5 rounded-full shadow-sm md:shadow-md whitespace-nowrap">
            🔥 Most Popular
          </div>
        </div>
      )}
      {/* Current Plan Badge */}
      {isCurrentPlan && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-20">
          <div className="bg-green-500 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-sm md:shadow-md whitespace-nowrap">
            Current Plan
          </div>
        </div>
      )}
        {/* Card Content */}
        <div className="flex flex-col h-full p-4 sm:p-5 md:p-6 lg:p-7">
          {/* Header */}
          <div className={cn('text-center', (popular || isCurrentPlan) ? 'pt-3 sm:pt-4' : 'pt-1 sm:pt-2')}>
            <h3
              id={`pricing-card-${name.toLowerCase().replace(/\s+/g, '-')}`}
              className="text-lg sm:text-xl md:text-2xl font-bold text-text-primary mb-2"
            >
              {name}
            </h3>
            <p className="text-text-secondary text-sm md:text-base mb-4 sm:mb-6">
              {description}
            </p>
          </div>

          {/* Price */}
          <div className="text-center mb-6 sm:mb-8">
            <div className="flex items-baseline justify-center gap-1">
              <span className="text-3xl sm:text-4xl md:text-5xl lg:text-4xl font-bold text-text-primary">
                {formatPrice(price)}
              </span>
              {interval && (
                <span className="text-text-secondary font-medium text-sm md:text-base">
                  /{interval}
                </span>
              )}
            </div>
          </div>

          {/* Features */}
          <div className="flex-1 mb-6 sm:mb-8">
            <ul className="space-y-2 sm:space-y-3 md:space-y-4" role="list">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center gap-3" role="listitem">
                  <div className="flex-shrink-0">
                    <Check className="w-4 h-4 md:w-5 md:h-5 text-link-default" aria-hidden="true" />
                  </div>
                  <span className="text-text-secondary text-sm md:text-base">
                    {feature}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Action Button */}
          <div className="mt-auto pt-4">
            <Button
              variant={isCurrentPlan ? 'secondary' : popular ? 'primary' : 'outline'}
              className="w-full group transition-all duration-200 py-3 md:py-4 text-sm md:text-base font-semibold"
              onClick={handleAction}
              isLoading={isLoading}
              disabled={disabled || isLoading}
              aria-label={`${isCurrentPlan ? 'Manage' : 'Subscribe to'} ${name} plan for ${formatPrice(price)} per ${interval}`}
            >
              <span className="flex items-center justify-center gap-2">
                {isCurrentPlan ? 'Manage Plan' : 'Subscribe'}
                <ArrowRight className="w-4 h-4 md:w-5 md:h-5 transition-transform group-hover:translate-x-0.5" />
              </span>
            </Button>
          </div>
        </div>
    </div>
  );
};