'use client';

import React from 'react';

export interface QuestionSkeletonProps {
  questionIndex: number;
  className?: string;
  title?: string;
  subtitle?: string;
}

export const QuestionSkeleton: React.FC<QuestionSkeletonProps> = ({
  questionIndex,
  className = '',
  title = 'AI is working...',
  subtitle = 'Please wait while we process your request',
}) => {
  return (
    <div className={`bg-white border border-gray-200 rounded-xl p-4 md:p-6 mb-4 md:mb-6 shadow-lg ${className}`}>
      {/* Header Section - matches regular question cards */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 md:mb-5 pb-2 md:pb-3 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-2 sm:mb-0">
          <span className="text-lg md:text-xl font-semibold text-gray-700">
            Question {questionIndex + 1}
          </span>
        </div>
        <div className="flex items-center gap-2 md:gap-3">
          {/* Subject Tag Skeleton */}
          <div className="h-6 w-20 bg-blue-100 rounded-full animate-pulse"></div>
          {/* Type Tag Skeleton */}
          <div className="h-6 w-24 bg-green-100 rounded-full animate-pulse"></div>
          {/* Dropdown Menu Skeleton */}
          <div className="h-8 w-8 bg-gray-100 rounded-md animate-pulse"></div>
        </div>
      </div>

      {/* Subtle Processing Section - matches question content area */}
      <div className="bg-gray-50 p-3 md:p-5 rounded-lg mb-3 md:mb-5">
        <div className="flex items-center justify-center space-x-3 py-4">
          {/* Simple loading spinner */}
          <div className="w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>

          {/* Processing Text */}
          <div className="text-center">
            <p className="text-sm md:text-base font-medium text-gray-700">
              {title}
            </p>
            <p className="text-xs md:text-sm text-gray-500 mt-1">
              {subtitle}
            </p>
          </div>
        </div>
      </div>

      {/* Options Skeleton - subtle and minimal */}
      <div className="space-y-1 mt-3 md:mt-5">
        {[1, 2, 3, 4].map((_, index) => (
          <div
            key={index}
            className="flex items-start space-x-2 md:space-x-3 py-1.5 md:py-3 border border-gray-100 rounded-md px-2 md:px-3"
          >
            <div className="w-4 h-4 bg-gray-200 rounded-full animate-pulse mt-0.5 md:mt-1"></div>
            <div className="flex-1 pt-0 md:pt-0.5">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Explanation Skeleton - matches the accordion style */}
      <div className="mt-3 md:mt-5 border border-gray-200 rounded-md overflow-hidden shadow-sm">
        <div className="w-full flex items-center justify-between py-2 md:py-2.5 px-3 md:px-4 bg-gray-50">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};
