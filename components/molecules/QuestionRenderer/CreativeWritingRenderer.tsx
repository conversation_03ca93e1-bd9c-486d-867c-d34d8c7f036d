'use client';

import React, { useState, useCallback } from 'react';
import { cn } from '@/utils/cn';

export interface CreativeWritingRendererProps {
  answer: string[];
  prompt?: string;
  isHtmlContent?: boolean;
  className?: string;
  minWords?: number;
  maxWords?: number;
  rubricCriteria?: string[];
}

export const CreativeWritingRenderer: React.FC<CreativeWritingRendererProps> = ({
  answer,
  prompt,
  isHtmlContent = false,
  className,
  minWords = 50,
  maxWords = 500,
  rubricCriteria = [],
}) => {
  const [showSample, setShowSample] = useState(false);

  return (
    <div className={cn('space-y-3 md:space-y-4', className)}>
      {/* Prompt */}
      {prompt && (
        <div className="bg-blue-50 p-3 md:p-4 rounded-lg border border-blue-200">
          <h3 className="font-semibold text-blue-900 mb-2 text-sm md:text-base">Writing Prompt</h3>
          {isHtmlContent ? (
            <div className="text-sm md:text-base" dangerouslySetInnerHTML={{ __html: prompt }} />
          ) : (
            <p className="text-blue-800 text-sm md:text-base">{prompt}</p>
          )}
        </div>
      )}

      {/* Sample Answer */}
      {answer.length > 0 && (
        <div className="border border-gray-200 rounded-lg">
          <button
            onClick={() => setShowSample(!showSample)}
            className="w-full p-2 md:p-3 text-left font-medium text-gray-700 hover:bg-gray-50 flex justify-between items-center text-sm md:text-base"
          >
            Sample Answer
            <span className={cn('transform transition-transform', showSample ? 'rotate-180' : '')}>
              ▼
            </span>
          </button>

          {showSample && (
            <div className="p-3 md:p-4 border-t border-gray-200 bg-gray-50">
              {answer.map((sampleAnswer, index) => (
                <div key={index} className="mb-3 last:mb-0">
                  {isHtmlContent ? (
                    <div className="text-xs md:text-sm" dangerouslySetInnerHTML={{ __html: sampleAnswer }} />
                  ) : (
                    <p className="text-gray-700 text-xs md:text-sm">{sampleAnswer}</p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
