'use client';

import React from 'react';
import { cn } from '@/utils/cn';

interface FillBlankContentProcessorProps {
  content: string;
  answers: string[];
  isHtmlContent?: boolean;
  className?: string;
}

export const FillBlankContentProcessor: React.FC<FillBlankContentProcessorProps> = ({
  content,
  answers,
  isHtmlContent = false,
  className = '',
}) => {
  const processFillBlankContent = (text: string, answerArray: string[]) => {
    if (!text) return [];

    // Enhanced regex to match various blank patterns
    const blankPatterns = /___|___\*|___\.|___,|___!|___\?|\[blank\]|\{blank\}|\(___\)/g;

    // Find all matches with their positions
    const matches = [];
    let match;
    while ((match = blankPatterns.exec(text)) !== null) {
      matches.push({
        pattern: match[0],
        index: match.index,
        length: match[0].length
      });
    }

    // Sort matches by their position in the text
    matches.sort((a, b) => a.index - b.index);

    const parts = [];
    let lastIndex = 0;
    let answerIndex = 0;

    // Process text segments and blanks in order
    for (const matchItem of matches) {
      // Add text segment before this blank if there is any
      if (matchItem.index > lastIndex) {
        parts.push({
          type: 'text',
          content: text.substring(lastIndex, matchItem.index),
        });
      }

      // Add the answer for this blank
      const answer = answerArray[answerIndex] || '';
      parts.push({
        type: 'answer',
        content: answer,
        originalPattern: matchItem.pattern,
      });

      answerIndex++;
      lastIndex = matchItem.index + matchItem.length;
    }

    // Add any remaining text after the last blank
    if (lastIndex < text.length) {
      parts.push({
        type: 'text',
        content: text.substring(lastIndex),
      });
    }

    return parts;
  };

  const contentParts = processFillBlankContent(content, answers);

  if (isHtmlContent) {
    // For HTML content, we need to process it differently
    // This is a simplified approach - in production, you might want to use a proper HTML parser
    let processedHtml = content;
    const blankPatterns = /___|___\*|___\.|___,|___!|___\?|\[blank\]|\{blank\}|\(___\)/g;
    
    let answerIndex = 0;
    processedHtml = processedHtml.replace(blankPatterns, () => {
      const answer = answers[answerIndex] || '';
      answerIndex++;
      return `<span class="inline-block px-2 py-1 mx-1 bg-blue-100 text-blue-800 rounded-md border border-blue-200 font-medium text-sm md:text-base">${answer}</span>`;
    });

    return (
      <div 
        className={cn('text-sm md:text-base text-gray-800 leading-relaxed', className)}
        dangerouslySetInnerHTML={{ __html: processedHtml }}
      />
    );
  }

  return (
    <div className={cn('text-sm md:text-base text-gray-800 leading-relaxed', className)}>
      {contentParts.map((part, index) => (
        <span key={index}>
          {part.type === 'text' ? (
            <span>{part.content}</span>
          ) : (
            <span className="inline-block px-2 py-1 mx-1 bg-blue-100 text-blue-800 rounded-md border border-blue-200 font-medium text-sm md:text-base">
              {part.content}
            </span>
          )}
        </span>
      ))}
    </div>
  );
};
