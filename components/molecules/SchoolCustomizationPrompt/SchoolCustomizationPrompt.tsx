'use client';

import React from 'react';
import { Settings, Palette, Upload, ArrowRight } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';
import { SchoolCustomizationAssessment, SchoolCustomizationLevel } from '@/utils/schoolCustomization';

interface SchoolCustomizationPromptProps {
  assessment: SchoolCustomizationAssessment;
  schoolName: string;
  className?: string;
  onCustomizeClick?: () => void;
}

export const SchoolCustomizationPrompt: React.FC<SchoolCustomizationPromptProps> = ({
  assessment,
  schoolName,
  className,
  onCustomizeClick
}) => {
  const isBasic = assessment.level === SchoolCustomizationLevel.BASIC;
  const isPartial = assessment.level === SchoolCustomizationLevel.PARTIALLY_CUSTOMIZED;

  // Don't show prompt for fully customized schools
  if (assessment.level === SchoolCustomizationLevel.FULLY_CUSTOMIZED) {
    return null;
  }

  const getPromptContent = () => {
    if (isBasic) {
      return {
        title: 'Make Your School Truly Yours!',
        subtitle: `Welcome to ${schoolName}`,
        description: 'Your school is ready to be personalized. Add your unique touch with custom branding, colors, and details to create a professional identity.',
        ctaText: 'Customize Your School',
        variant: 'primary' as const,
        urgency: 'high' as const
      };
    } else {
      return {
        title: 'Complete Your School Setup',
        subtitle: `${schoolName} is looking good!`,
        description: 'You\'ve made great progress customizing your school. Add the finishing touches to make it perfect.',
        ctaText: 'Finish Customization',
        variant: 'secondary' as const,
        urgency: 'medium' as const
      };
    }
  };

  const content = getPromptContent();

  const getQuickActions = () => {
    const actions = [];
    
    if (assessment.missingCustomizations.includes('School logo')) {
      actions.push({ icon: Upload, text: 'Add Logo', key: 'logo' });
    }
    
    if (assessment.missingCustomizations.includes('Brand colors')) {
      actions.push({ icon: Palette, text: 'Set Colors', key: 'colors' });
    }
    
    if (assessment.missingCustomizations.includes('Custom school name')) {
      actions.push({ icon: Settings, text: 'Update Details', key: 'details' });
    }

    return actions.slice(0, 3); // Show max 3 quick actions
  };

  const quickActions = getQuickActions();

  return (
    <div className={cn(
      'card bg-gradient-to-br from-primary/5 to-secondary/5 border border-primary/20 shadow-lg',
      className
    )}>
      <div className="card-body">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <div className={cn(
                'badge badge-sm',
                isBasic ? 'badge-warning' : 'badge-info'
              )}>
                {isBasic ? 'Setup Needed' : 'In Progress'}
              </div>
              <div className="text-sm text-base-content/60">
                {assessment.score}% Complete
              </div>
            </div>
            <h3 className="card-title text-xl mb-1">{content.title}</h3>
            <p className="text-base-content/80 text-sm">{content.subtitle}</p>
          </div>
          
          {/* Progress indicator */}
          <div className="radial-progress text-primary text-xs" 
               style={{"--value": assessment.score, "--size": "3rem"} as React.CSSProperties}>
            {assessment.score}%
          </div>
        </div>

        {/* Description */}
        <p className="text-base-content/70 mb-6">
          {content.description}
        </p>

        {/* Quick Actions */}
        {quickActions.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-base-content/80 mb-3">Quick Actions:</h4>
            <div className="flex flex-wrap gap-2">
              {quickActions.map((action) => (
                <button
                  key={action.key}
                  className="btn btn-sm btn-outline btn-primary"
                  onClick={onCustomizeClick}
                >
                  <action.icon className="w-4 h-4" />
                  {action.text}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Main CTA */}
        <div className="card-actions justify-end">
          <Button
            variant={content.variant}
            onClick={onCustomizeClick}
            href="/school-customization"
            className="btn-lg gap-2"
            iconProps={{
              variant: 'arrow-right'
            }}
          >
            {content.ctaText}
          </Button>
        </div>

        {/* Benefits list for basic schools */}
        {isBasic && (
          <div className="mt-4 pt-4 border-t border-base-300">
            <div className="text-xs text-base-content/60">
              <span className="font-medium">Benefits:</span> Professional appearance, Brand recognition, Student engagement
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SchoolCustomizationPrompt;
