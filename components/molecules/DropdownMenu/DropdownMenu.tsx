'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/utils/cn';
import Icon from '@/components/atoms/Icon';

export interface DropdownMenuItem {
  id: string;
  label: string;
  icon?: string;
  onClick: () => void;
  variant?: 'default' | 'danger';
  disabled?: boolean;
}

export interface DropdownMenuProps {
  items: DropdownMenuItem[];
  trigger?: React.ReactNode;
  className?: string;
  menuClassName?: string;
  placement?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

export const DropdownMenu: React.FC<DropdownMenuProps> = ({
  items,
  trigger,
  className = '',
  menuClassName = '',
  placement = 'bottom-right',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen]);

  const handleItemClick = (item: DropdownMenuItem) => {
    if (!item.disabled) {
      item.onClick();
      setIsOpen(false);
    }
  };

  const getPlacementClasses = () => {
    switch (placement) {
      case 'bottom-left':
        return 'top-full left-0 mt-1';
      case 'top-right':
        return 'bottom-full right-0 mb-1';
      case 'top-left':
        return 'bottom-full left-0 mb-1';
      case 'bottom-right':
      default:
        return 'top-full right-0 mt-1';
    }
  };

  const defaultTrigger = (
    <button
      onClick={() => setIsOpen(!isOpen)}
      className="p-1.5 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
      aria-label="More options"
    >
      {/* Using three dots as more-horizontal equivalent */}
      <div className="flex items-center justify-center w-4 h-4">
        <div className="flex gap-0.5">
          <div className="w-1 h-1 bg-current rounded-full"></div>
          <div className="w-1 h-1 bg-current rounded-full"></div>
          <div className="w-1 h-1 bg-current rounded-full"></div>
        </div>
      </div>
    </button>
  );

  return (
    <div ref={dropdownRef} className={cn('relative inline-block', className)}>
      {/* Trigger */}
      <div onClick={() => setIsOpen(!isOpen)}>
        {trigger || defaultTrigger}
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className={cn(
            'absolute z-50 min-w-[160px] bg-white border border-gray-200 rounded-lg shadow-lg py-1',
            getPlacementClasses(),
            menuClassName
          )}
        >
          {items.map((item) => (
            <button
              key={item.id}
              onClick={() => handleItemClick(item)}
              disabled={item.disabled}
              className={cn(
                'w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors',
                'hover:bg-gray-50 focus:bg-gray-50 focus:outline-none',
                item.variant === 'danger' 
                  ? 'text-red-600 hover:bg-red-50 focus:bg-red-50' 
                  : 'text-gray-700',
                item.disabled && 'opacity-50 cursor-not-allowed hover:bg-transparent'
              )}
            >
              {item.icon && (
                <Icon 
                  variant={item.icon as any} 
                  size={3.5} 
                  className={cn(
                    item.variant === 'danger' ? 'text-red-500' : 'text-gray-500'
                  )}
                />
              )}
              {item.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
