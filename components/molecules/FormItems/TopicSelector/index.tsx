'use client';

import React, { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { fetchSubjectHierarchy, TopicHierarchy } from '@/actions/topic.action';
import RHFCheckboxGroup, { CheckboxOption } from '../RHFCheckboxGroup';

export type TopicSelectorProps = {
  name: string;
  question: string;
  hidden?: boolean;
};

export const TopicSelector: React.FC<TopicSelectorProps> = ({
  name,
  question,
  hidden,
}) => {
  const { watch, getValues } = useFormContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<any>(null);
  const [topics, setTopics] = useState<CheckboxOption[]>([]);

  // Try different possible field names for grade and subject
  const gradeId = watch('grade');
  const topicId = watch('topic');

  // Convert API response to CheckboxOption format
  const convertToCheckboxOptions = (
    data: TopicHierarchy[]
  ): CheckboxOption[] => {
    return data.map((topic) => ({
      label: topic.name,
      value: topic.id,
      children: topic.children.map((parent) => ({
        label: parent.name,
        value: parent.id,
        children: parent.children.map((lesson) => ({
          label: lesson.name,
          value: lesson.id,
          children: [],
        })),
      })),
    }));
  };

  // Fetch topics when grade and subject change
  useEffect(() => {
    const fetchTopics = async () => {
      if (!gradeId || !topicId) {
        setTopics([]);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const response = await fetchSubjectHierarchy(gradeId.value, topicId.value);
        if (response.status === 'error') {
          setError(
            response.message
              ? typeof response.message === 'string'
                ? response.message
                : 'Validation errors occurred'
              : 'Failed to fetch topics'
          );
          setTopics([]);
        } else {
          const options = convertToCheckboxOptions(response.data);
          setTopics(options);
        }
      } catch (err) {
        setError('An unexpected error occurred');
        setTopics([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTopics();
  }, [getValues, gradeId, topicId]);

  if (hidden) return null;

  if (loading) {
    return (
      <div className="flex flex-col gap-6 border rounded-lg border-gray-200 p-6">
        <div className="text-xl font-semibold select-none w-fit">
          {question}
        </div>
        <div className="flex justify-center items-center p-8">
          <div className="loading loading-spinner loading-lg text-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col gap-6 border rounded-lg border-gray-200 p-6">
        <div className="text-xl font-semibold select-none w-fit">
          {question}
        </div>
        <div className="alert alert-error">
          <span>
            {typeof error === 'string'
              ? error
              : 'An error occurred while fetching topics'}
          </span>
        </div>
      </div>
    );
  }

  // const uiSubjectIdValue = uiFormValues.subjectId;

  // Use the first non-undefined value for UI rendering
  if (!gradeId || !topicId) {
    return (
      <div className="flex flex-col gap-6 border rounded-lg border-gray-200 p-6">
        <div className="text-xl font-semibold select-none w-fit">
          {question}
        </div>
        <div className="alert alert-info">
          <span>Please select a grade and subject first</span>
        </div>
      </div>
    );
  }

  if (topics.length === 0) {
    return (
      <div className="flex flex-col gap-6 border rounded-lg border-gray-200 p-6">
        <div className="text-xl font-semibold select-none w-fit">
          {question}
        </div>
        <div className="alert alert-warning">
          <span>No topics found for the selected grade and subject</span>
        </div>
      </div>
    );
  }

  return <RHFCheckboxGroup 
    name={name} 
    question={question} 
    options={topics[0].children as CheckboxOption[]} 
    structuredData={true} 
  />;
};

export default TopicSelector;
