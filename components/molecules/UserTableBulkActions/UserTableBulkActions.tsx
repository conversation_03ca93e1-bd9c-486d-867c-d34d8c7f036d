'use client';

import React from 'react';
import { Mail, Trash2 } from 'lucide-react';

export interface UserTableBulkActionsProps {
  selectedCount: number;
  onClearSelection: () => void;
  onBulkEmail: () => void;
  onBulkDelete: () => void;
}

export const UserTableBulkActions: React.FC<UserTableBulkActionsProps> = ({
  selectedCount,
  onClearSelection,
  onBulkEmail,
  onBulkDelete
}) => {
  return (
    <div
      className="bg-section-bg-accent border-b border-blue-200 p-3 flex justify-between items-center"
      style={{
        animation: 'slideDown 0.3s ease-in-out',
        transition: 'all 0.3s ease-in-out'
      }}
    >
      <div className="flex items-center gap-3">
        <span className="text-sm font-medium text-text-primary">
          {selectedCount} {selectedCount === 1 ? 'user' : 'users'} selected
        </span>
        <button
          onClick={onClearSelection}
          className="text-link-default hover:text-link-hover text-sm font-medium"
        >
          Clear selection
        </button>
      </div>
      <div className="flex items-center gap-2">
        <button
          onClick={onBulkEmail}
          className="px-3 py-1.5 rounded-lg bg-white border border-gray-300 text-gray-700 text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center gap-1.5"
          aria-label="Email selected users"
        >
          <Mail size={16} />
          Email
        </button>
        <button
          onClick={onBulkDelete}
          className="px-3 py-1.5 rounded-lg bg-white border border-red-300 text-red-600 text-sm font-medium hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 flex items-center gap-1.5"
          aria-label="Delete selected users"
        >
          <Trash2 size={16} />
          Delete
        </button>
      </div>
    </div>
  );
};