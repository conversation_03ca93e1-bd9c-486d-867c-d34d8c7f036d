'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { cn } from '@/utils/cn';
import { Edit, Trash2, Eye } from 'lucide-react';

interface SwipeAction {
  icon: React.ReactNode;
  label: string;
  color: 'blue' | 'red' | 'green' | 'yellow';
  onClick: () => void;
}

interface SwipeableCardProps {
  children: React.ReactNode;
  actions?: SwipeAction[];
  className?: string;
  disabled?: boolean;
}

export const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  actions = [],
  className,
  disabled = false,
}) => {
  const [translateX, setTranslateX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const cardRef = useRef<HTMLDivElement>(null);
  const actionsRef = useRef<HTMLDivElement>(null);

  const maxSwipe = actions.length * 80; // 80px per action

  const handleTouchStart = (e: React.TouchEvent) => {
    if (disabled) return;
    setIsDragging(true);
    setStartX(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || disabled) return;
    
    const currentX = e.touches[0].clientX;
    const diff = startX - currentX;
    
    // Only allow left swipe (positive diff)
    if (diff > 0) {
      setTranslateX(Math.min(diff, maxSwipe));
    } else {
      setTranslateX(0);
    }
  };

  const handleTouchEnd = () => {
    if (!isDragging || disabled) return;
    setIsDragging(false);
    
    // Snap to position
    if (translateX > maxSwipe / 2) {
      setTranslateX(maxSwipe);
    } else {
      setTranslateX(0);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (disabled) return;
    setIsDragging(true);
    setStartX(e.clientX);
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || disabled) return;

    const diff = startX - e.clientX;

    if (diff > 0) {
      setTranslateX(Math.min(diff, maxSwipe));
    } else {
      setTranslateX(0);
    }
  }, [isDragging, disabled, startX, maxSwipe]);

  const handleMouseUp = useCallback(() => {
    if (!isDragging || disabled) return;
    setIsDragging(false);

    if (translateX > maxSwipe / 2) {
      setTranslateX(maxSwipe);
    } else {
      setTranslateX(0);
    }
  }, [isDragging, disabled, translateX, maxSwipe]);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, startX, translateX, handleMouseMove, handleMouseUp]);

  const getActionColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-500 hover:bg-blue-600 text-white',
      red: 'bg-red-500 hover:bg-red-600 text-white',
      green: 'bg-green-500 hover:bg-green-600 text-white',
      yellow: 'bg-yellow-500 hover:bg-yellow-600 text-white',
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Actions background */}
      {actions.length > 0 && (
        <div
          ref={actionsRef}
          className="absolute right-0 top-0 h-full flex"
          style={{ width: `${maxSwipe}px` }}
        >
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={() => {
                action.onClick();
                setTranslateX(0); // Reset position after action
              }}
              className={cn(
                'w-20 h-full flex flex-col items-center justify-center text-xs font-medium transition-colors',
                getActionColor(action.color)
              )}
              style={{ 
                transform: `translateX(${Math.max(0, translateX - (index + 1) * 80)}px)`,
                transition: isDragging ? 'none' : 'transform 0.3s ease'
              }}
            >
              {action.icon}
              <span className="mt-1">{action.label}</span>
            </button>
          ))}
        </div>
      )}

      {/* Main content */}
      <div
        ref={cardRef}
        className={cn(
          'relative bg-white transition-transform',
          isDragging ? 'transition-none' : 'transition-transform duration-300 ease-out'
        )}
        style={{ 
          transform: `translateX(-${translateX}px)`,
          touchAction: 'pan-y' // Allow vertical scrolling
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
      >
        {children}
      </div>
    </div>
  );
};

// Example usage component
export const SwipeableUserCard: React.FC<{
  user: any;
  onEdit: () => void;
  onDelete: () => void;
  onView: () => void;
}> = ({ user, onEdit, onDelete, onView }) => {
  const actions: SwipeAction[] = [
    {
      icon: <Eye className="w-5 h-5" />,
      label: 'View',
      color: 'blue',
      onClick: onView,
    },
    {
      icon: <Edit className="w-5 h-5" />,
      label: 'Edit',
      color: 'green',
      onClick: onEdit,
    },
    {
      icon: <Trash2 className="w-5 h-5" />,
      label: 'Delete',
      color: 'red',
      onClick: onDelete,
    },
  ];

  return (
    <SwipeableCard actions={actions} className="mb-3">
      <div className="p-4 border border-gray-200 rounded-lg bg-white">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center">
            {user.name.charAt(0)}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-gray-900 truncate">{user.name}</h3>
            <p className="text-sm text-gray-500 truncate">{user.email}</p>
          </div>
        </div>
      </div>
    </SwipeableCard>
  );
};
