'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFormState } from 'react-dom';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Mail, ArrowLeft, CheckCircle2 } from 'lucide-react';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Icon } from '@/components/atoms';

import { ForgotPasswordValues, forgotPasswordSchema } from './ForgotPasswordForm.schema';
import { forgotPasswordAction } from '@/actions/auth.action';
import { APP_ROUTE } from '@/constants/route.constant';
import { cn } from '@/utils/cn';

export default function ForgotPasswordForm() {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [state, formAction] = useFormState(forgotPasswordAction, {
    success: false,
    message: '',
    errors: {},
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
  } = useForm<ForgotPasswordValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const watchedEmail = watch('email');

  // Handle form submission tracking
  useEffect(() => {
    if (state.success && !isSubmitted) {
      setIsSubmitted(true);
    }
  }, [state.success, isSubmitted]);

  const onSubmit = async (data: ForgotPasswordValues) => {
    // Create FormData for server action
    const formData = new FormData();
    formData.append('email', data.email);
    
    // Call server action
    formAction(formData);
  };

  // Success state
  if (isSubmitted && state.success) {
    return (
      <div className="w-full max-w-md mx-auto px-4 sm:px-0">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <div className="relative">
            {/* Gradient Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-section-bg-accent via-background-default to-background-subtle rounded-xl sm:rounded-2xl"></div>

            {/* Glass Effect Overlay */}
            <div className="relative bg-background-default/80 backdrop-blur-sm border border-white/20 rounded-xl sm:rounded-2xl shadow-xl shadow-text-primary/10 p-6 sm:p-8">
              {/* Success Animation */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                    <CheckCircle2 size={32} className="text-white" />
                  </div>
                </div>
              </motion.div>

              {/* Success Content */}
              <div className="text-center space-y-4">
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <h2 className="text-xl sm:text-2xl font-semibold text-text-primary">
                    Check your email
                  </h2>
                </motion.div>
                
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  <p className="text-text-secondary text-sm sm:text-base leading-relaxed">
                    {state.message}
                  </p>
                </motion.div>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <div className="pt-4">
                    <p className="text-xs text-text-secondary mb-4">
                      Didn&apos;t receive the email? Check your spam folder or try again.
                    </p>
                    
                    <div className="space-y-3">
                      <Button
                        onClick={() => {
                          setIsSubmitted(false);
                          // Reset form state by reloading - simple but effective
                          window.location.reload();
                        }}
                        variant="outline"
                        className="w-full"
                      >
                        Try again
                      </Button>
                      
                      <Link href={APP_ROUTE.AUTH.SIGN_IN} className="block">
                        <Button 
                        variant="ghost" 
                        className="w-full"
                        iconProps={{
                          variant: 'arrow-left',
                          className: 'mr-2'
                        }}
                        >
                          Back to Sign In
                        </Button>
                      </Link>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  // Main form state
  return (
    <div className="w-full max-w-md mx-auto px-4 sm:px-0">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="space-y-4 sm:space-y-6">
          {/* Error Alert */}
          {!state.success && state.message && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="error"
                message={state.message}
              />
            </motion.div>
          )}

          {/* Main Form Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="relative">
              {/* Gradient Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-section-bg-accent via-background-default to-background-subtle rounded-xl sm:rounded-2xl"></div>

              {/* Glass Effect Overlay */}
              <div className="relative bg-background-default/80 backdrop-blur-sm border border-white/20 rounded-xl sm:rounded-2xl shadow-xl shadow-text-primary/10 p-4 sm:p-6">
                {/* Decorative Elements */}
                <div className="absolute top-0 right-0 w-24 sm:w-32 h-24 sm:h-32 bg-gradient-to-br from-link-default/10 to-purple-400/10 rounded-full -mr-12 sm:-mr-16 -mt-12 sm:-mt-16"></div>
                <div className="absolute bottom-0 left-0 w-20 sm:w-24 h-20 sm:h-24 bg-gradient-to-tr from-green-400/10 to-link-default/10 rounded-full -ml-10 sm:-ml-12 -mb-10 sm:-mb-12"></div>

                {/* Header */}
                <div className="text-center mb-6 relative z-10">
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <h1 className="text-lg sm:text-xl font-semibold flex max-sm:flex-col max-sm:gap-0.5 justify-center items-center gap-2 text-text-primary mb-2">
                      Forgot your password?
                    </h1>
                  </motion.div>
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <p className="text-xs sm:text-sm text-text-secondary">
                      No worries! Enter your email and we&apos;ll send you a reset link.
                    </p>
                  </motion.div>
                </div>

                {/* Form */}
                <form 
                  onSubmit={handleSubmit(onSubmit)} 
                  className="space-y-4 relative z-10" 
                  role="form" 
                  aria-label="Forgot password form"
                >
                  {/* Email Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <FormField
                      label="Email Address"
                      error={errors.email?.message || (state.errors?.email ? state.errors.email[0] : undefined)}
                      required
                    >
                      <InputWithIcon
                        type="email"
                        placeholder="Enter your email address"
                        leftIcon={<Mail size={16} className="sm:size-[18px] text-link-default" />}
                        className={cn(
                          'h-10 sm:h-12 text-sm sm:text-base border-2 rounded-lg sm:rounded-xl transition-all duration-300 focus:ring-4 focus:ring-accent-bg-light hover:border-link-hover',
                          'bg-background-default/50 backdrop-blur-sm',
                          (errors.email || state.errors?.email)
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-link-default'
                        )}
                        aria-describedby={errors.email ? 'email-error' : undefined}
                        {...register('email')}
                      />
                    </FormField>
                  </motion.div>

                  {/* Submit Button */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    <Button
                      type="submit"
                      className="w-full h-10 sm:h-12 text-sm sm:text-base font-medium rounded-lg sm:rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl"
                      disabled={isSubmitting || !watchedEmail?.trim()}
                      isLoading={isSubmitting}
                    >
                      {isSubmitting ? 'Sending Reset Link...' : 'Send Reset Link'}
                    </Button>
                  </motion.div>

                  {/* Back to Sign In */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <div className="text-center pt-2">
                      <Link 
                        href={APP_ROUTE.AUTH.SIGN_IN}
                        className="inline-flex items-center text-sm text-link-default hover:text-link-hover transition-colors duration-200"
                      >
                        <ArrowLeft size={14} className="mr-1" />
                        Back to Sign In
                      </Link>
                    </div>
                  </motion.div>
                </form>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
} 