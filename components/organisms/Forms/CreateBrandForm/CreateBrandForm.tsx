'use client';

import React, { useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/atoms/Button/Button';
import { Input } from '@/components/atoms/Input/Input';
import { Form } from '@/components/atoms/Form/Form';
import { Label } from '@/components/atoms/Label/Label';
import { handleCreateBrandAction } from '@/actions/brand.action'; // Import server action
import { useBrandSessionUpdate } from '@/hooks/useBrandSessionUpdate'; // Import brand session hook

interface ICreateBrandFormInput {
  logo?: string;
  color?: string;
  image?: string;
}

const createBrandSchema = z.object({
  logo: z.string().url({ message: 'Invalid URL format for logo' }).optional().or(z.literal('')),
  color: z.string().regex(/^#([0-9A-Fa-f]{3}){1,2}$/, { message: 'Invalid color hex code' }).optional().or(z.literal('')),
  image: z.string().url({ message: 'Invalid URL format for image' }).optional().or(z.literal('')),
});

export const CreateBrandForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { updateSessionBrand } = useBrandSessionUpdate(); // Use brand session hook

  const methods = useForm<ICreateBrandFormInput>({
    resolver: zodResolver(createBrandSchema),
  });

  const { register, handleSubmit, formState: { errors }, reset } = methods;

  const onSubmitHandler: SubmitHandler<ICreateBrandFormInput> = async (formData) => {
    setIsLoading(true);
    setError(null);
    try {
      const payload = {
        ...formData,
        logo: formData.logo === '' ? undefined : formData.logo,
        color: formData.color === '' ? undefined : formData.color,
        image: formData.image === '' ? undefined : formData.image,
      };
      const result = await handleCreateBrandAction(payload);
      if (result.status === 'success' && result.data) {
        // Update session with the new brand data
        const sessionUpdateSuccess = await updateSessionBrand(result.data);

        if (sessionUpdateSuccess) {
          alert('Brand created successfully and session updated!');
        } else {
          alert('Brand created successfully! (Session update failed, but brand was created)');
        }

        reset(); // Reset the form on success
      } else {
        // result.status === 'error', so we can safely access message
        const errorResult = result as { status: 'error'; message: string | Array<{field: string; constraints: string}> };
        const errorMessage = Array.isArray(errorResult.message)
          ? errorResult.message.map((m: any) => `${m.field}: ${m.constraints}`).join(', ')
          : errorResult.message;
        setError(errorMessage as string || 'Failed to create brand.');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...methods}>
      <form onSubmit={handleSubmit(onSubmitHandler)} className="space-y-4">
        <div>
          <Label htmlFor="logo">Logo URL (Optional)</Label>
          <Input id="logo" {...register('logo')} placeholder="https://example.com/logo.png" />
          {errors.logo && <p className="text-red-500 text-sm">{errors.logo.message}</p>}
        </div>

        <div>
          <Label htmlFor="color">Brand Color (Optional)</Label>
          <Input id="color" {...register('color')} placeholder="#FF5733" />
          {errors.color && <p className="text-red-500 text-sm">{errors.color.message}</p>}
        </div>

        <div>
          <Label htmlFor="image">Brand Image URL (Optional)</Label>
          <Input id="image" {...register('image')} placeholder="https://example.com/image.png" />
          {errors.image && <p className="text-red-500 text-sm">{errors.image.message}</p>}
        </div>

        {error && <p className="text-red-500 text-sm">{error}</p>}

        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Creating...' : 'Create Brand'}
        </Button>
      </form>
    </Form>
  );
};
