'use client';

import React from 'react';
import { FileText } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';

interface NarrativeStructureEmptyProps {
  onExtractClick: () => void;
  isExtracting: boolean;
}

export const NarrativeStructureEmpty: React.FC<NarrativeStructureEmptyProps> = ({
  onExtractClick,
  isExtracting,
}) => {
  return (
    <div className="w-full flex flex-col items-center justify-center py-8 px-6 bg-background-default rounded-lg border border-gray-200 shadow-sm">
      <div className="w-16 h-16 bg-section-bg-accent rounded-full flex items-center justify-center mb-4 shadow-sm">
        <FileText className="text-link-default" size={28} />
      </div>
      <h4 className="text-base font-medium text-text-primary mb-2">No Narrative Structure Available</h4>
      <p className="text-sm text-text-secondary mb-6 text-center max-w-md">
        Extract the narrative structure to view the content organization and improve your educational materials.
      </p>
      <Button
        variant="primary"
        onClick={onExtractClick}
        isLoading={isExtracting}
        iconProps={{
          variant: 'download',
          className: 'w-4'
        }}
        className="text-sm py-2 px-4 shadow-sm"
      >
        {isExtracting ? 'Extracting...' : 'Extract Structure'}
      </Button>
    </div>
  );
};