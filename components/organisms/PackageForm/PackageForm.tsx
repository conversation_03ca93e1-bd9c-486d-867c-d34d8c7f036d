'use client';

import React, { useState, useEffect } from 'react';
import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/atoms/Button/Button';
import { Input } from '@/components/atoms/Input/Input';
import { Form } from '@/components/atoms/Form/Form';
import { FormField } from '@/components/molecules/FormField/FormField';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/atoms/Dialog/Dialog';
import { handleCreatePackageAction, handleUpdatePackageAction } from '@/actions/package.action';
import { IPackageResponse, ICreatePackagePayload, IUpdatePackagePayload } from '@/apis/packageApi';
import { Package, DollarSign, FileText, Image, CreditCard, Clock } from 'lucide-react';

// Form input interface matching the API structure
interface IPackageFormInput {
  name: string;
  description?: string;
  image?: string;
  stripeProductId?: string;
  // Price configuration
  unitAmount: number;
  currency: string;
  type: 'one_time' | 'recurring';
  interval?: 'day' | 'week' | 'month' | 'year';
  intervalCount?: number;
  nickname?: string;
  active: boolean;
}

const packageSchema = z.object({
  name: z.string().min(1, { message: 'Package name is required' }),
  description: z.string().optional(),
  image: z.string().url().optional().or(z.literal('')),
  stripeProductId: z.string().optional(),
  // Price validation
  unitAmount: z.number().min(1, { message: 'Price must be at least $0.01' }),
  currency: z.string().min(3).max(3, { message: 'Currency must be 3 characters (e.g., USD)' }),
  type: z.enum(['one_time', 'recurring'], { message: 'Please select a price type' }),
  interval: z.enum(['day', 'week', 'month', 'year']).optional(),
  intervalCount: z.number().min(1).optional(),
  nickname: z.string().optional(),
  active: z.boolean(),
}).refine((data) => {
  // If type is recurring, interval is required
  if (data.type === 'recurring' && !data.interval) {
    return false;
  }
  return true;
}, {
  message: 'Interval is required for recurring prices',
  path: ['interval'],
});

interface PackageFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: 'create' | 'edit';
  packageData?: IPackageResponse | null;
  onSuccess?: () => void;
}

export const PackageForm: React.FC<PackageFormProps> = ({
  open,
  onOpenChange,
  mode,
  packageData,
  onSuccess,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const methods = useForm<IPackageFormInput>({
    resolver: zodResolver(packageSchema),
    defaultValues: {
      name: '',
      description: '',
      image: '',
      stripeProductId: '',
      unitAmount: 0,
      currency: 'USD',
      type: 'recurring',
      interval: 'month',
      intervalCount: 1,
      nickname: '',
      active: true,
    },
  });

  const { register, handleSubmit, reset, setValue, watch, formState: { errors } } = methods;
  const watchType = watch('type');

  // Helper to format price display (convert cents to dollars for display)
  const formatPriceForDisplay = (unitAmount: number): number => {
    return unitAmount / 100;
  };

  // Helper to convert dollars to cents for API
  const convertToCents = (dollars: number): number => {
    return Math.round(dollars * 100);
  };

  // Reset form when modal opens and set data for edit mode
  useEffect(() => {
    if (open) {
      setError(null);
      if (mode === 'edit' && packageData) {
        const firstPrice = packageData.prices?.[0];
        reset({
          name: packageData.name,
          description: packageData.description || '',
          image: packageData.image || '',
          stripeProductId: packageData.stripeProductId || '',
          unitAmount: firstPrice ? formatPriceForDisplay(firstPrice.unitAmount) : 0,
          currency: firstPrice?.currency || 'USD',
          type: firstPrice?.type || 'recurring',
          interval: firstPrice?.interval || 'month',
          intervalCount: firstPrice?.intervalCount || 1,
          nickname: firstPrice?.nickname || '',
          active: firstPrice?.active ?? true,
        });
      } else {
        reset({
          name: '',
          description: '',
          image: '',
          stripeProductId: '',
          unitAmount: 0,
          currency: 'USD',
          type: 'recurring',
          interval: 'month',
          intervalCount: 1,
          nickname: '',
          active: true,
        });
      }
    }
  }, [open, mode, packageData, reset]);

  const onSubmitHandler: SubmitHandler<IPackageFormInput> = async (formData) => {
    setIsLoading(true);
    setError(null);

    try {
      // Convert form data to API payload structure
      const priceData = {
        currency: formData.currency.toUpperCase(),
        unitAmount: convertToCents(formData.unitAmount),
        nickname: formData.nickname || undefined,
        active: formData.active,
        type: formData.type,
        ...(formData.type === 'recurring' && {
          interval: formData.interval,
          intervalCount: formData.intervalCount || 1,
        }),
      };

      const payload: ICreatePackagePayload | IUpdatePackagePayload = {
        name: formData.name,
        description: formData.description || undefined,
        image: formData.image || undefined,
        stripeProductId: formData.stripeProductId || undefined,
        prices: [priceData],
      };

      let result;
      
      if (mode === 'create') {
        result = await handleCreatePackageAction(payload as ICreatePackagePayload);
      } else {
        if (!packageData?.id) {
          throw new Error('Package ID is required for editing');
        }
        result = await handleUpdatePackageAction(packageData.id, payload as IUpdatePackagePayload);
      }

      if (result.status === 'success') {
        onOpenChange(false);
        onSuccess?.();
      } else {
        const errorMessage = Array.isArray(result.message) 
          ? result.message.map(m => typeof m === 'object' && m.field && m.constraints 
              ? `${m.field}: ${m.constraints}` 
              : String(m)).join(', ') 
          : result.message;
        setError(errorMessage || `Failed to ${mode} package.`);
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {mode === 'create' ? 'Create New Package' : 'Edit Package'}
          </DialogTitle>
        </DialogHeader>

        <Form {...methods}>
          <form onSubmit={handleSubmit(onSubmitHandler)} className="space-y-6 p-4">
            {/* Error Display */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Package Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Package Information</h3>
              
              {/* Package Name */}
              <FormField
                label="Package Name"
                required
                error={errors.name?.message}
                icon={<Package size={16} />}
              >
                <Input
                  {...register('name')}
                  placeholder="Enter package name"
                  className="pl-10"
                />
              </FormField>

              {/* Description */}
              <FormField
                label="Description"
                error={errors.description?.message}
                icon={<FileText size={16} />}
              >
                <textarea
                  {...register('description')}
                  placeholder="Enter package description"
                  className="w-full min-h-[80px] px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </FormField>

              {/* Image URL */}
              <FormField
                label="Image URL"
                error={errors.image?.message}
                icon={<Image size={16} />}
              >
                <Input
                  {...register('image')}
                  placeholder="https://example.com/image.jpg"
                  className="pl-10"
                />
              </FormField>

              {/* Stripe Product ID */}
              <FormField
                label="Stripe Product ID"
                error={errors.stripeProductId?.message}
                icon={<CreditCard size={16} />}
              >
                <Input
                  {...register('stripeProductId')}
                  placeholder="prod_xxxxxxxxxxxxx (optional)"
                  className="pl-10"
                />
              </FormField>
            </div>

            {/* Pricing Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Pricing Information</h3>
              
              {/* Price and Currency */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Price (USD)"
                  required
                  error={errors.unitAmount?.message}
                  icon={<DollarSign size={16} />}
                >
                  <Input
                    {...register('unitAmount', { valueAsNumber: true })}
                    type="number"
                    step="0.01"
                    min="0.01"
                    placeholder="9.99"
                    className="pl-10"
                  />
                </FormField>

                <FormField
                  label="Currency"
                  required
                  error={errors.currency?.message}
                >
                  <select
                    {...register('currency')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="SGD">SGD</option>
                  </select>
                </FormField>
              </div>

              {/* Price Type */}
              <FormField
                label="Price Type"
                required
                error={errors.type?.message}
              >
                <select
                  {...register('type')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="one_time">One-time Payment</option>
                  <option value="recurring">Recurring Subscription</option>
                </select>
              </FormField>

              {/* Recurring Options */}
              {watchType === 'recurring' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    label="Billing Interval"
                    required
                    error={errors.interval?.message}
                    icon={<Clock size={16} />}
                  >
                    <select
                      {...register('interval')}
                      className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="day">Daily</option>
                      <option value="week">Weekly</option>
                      <option value="month">Monthly</option>
                      <option value="year">Yearly</option>
                    </select>
                  </FormField>

                  <FormField
                    label="Interval Count"
                    error={errors.intervalCount?.message}
                  >
                    <Input
                      {...register('intervalCount', { valueAsNumber: true })}
                      type="number"
                      min="1"
                      placeholder="1"
                    />
                  </FormField>
                </div>
              )}

              {/* Price Nickname */}
              <FormField
                label="Price Nickname"
                error={errors.nickname?.message}
              >
                <Input
                  {...register('nickname')}
                  placeholder="e.g., Standard Plan, Premium Tier"
                />
              </FormField>

              {/* Active Status */}
              <FormField
                label="Price Status"
                error={errors.active?.message}
              >
                <div className="flex items-center space-x-2">
                  <input
                    {...register('active')}
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="text-sm font-medium text-gray-700">
                    Active (customers can purchase this price)
                  </label>
                </div>
              </FormField>
            </div>

            <DialogFooter className="flex justify-end gap-3 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    {mode === 'create' ? 'Creating...' : 'Updating...'}
                  </div>
                ) : (
                  mode === 'create' ? 'Create Package' : 'Update Package'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}; 