'use client'; // Make it a client component

// import { getWorksheets, TWorksheet } from '@/apis/worksheet'; // No longer using this directly
import { TWorksheet } from '@/apis/worksheet'; // Keep TWorksheet type
import { getWorksheetsAction } from '@/actions/worksheet.action'; // Import the server action
import { MobileOptimizedWorksheetTable } from '@/components/molecules/ManageWorksheet/WorksheetTable/MobileOptimizedWorksheetTable';
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { ManageWorksheetHeader } from '../ManageWorksheetHeader/ManageWorksheetHeader';
import { useEffect, useState, useCallback } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation'; // Added for URL sync
import { TPagination } from '@/@types/pagination';
import { PaginationState } from '@tanstack/react-table'; // For TableCommon

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE_INDEX = 0;

export const WorksheetListing: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [worksheetsData, setWorksheetsData] = useState<TPagination<TWorksheet> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  // Initialize pagination state from URL search params or defaults
  const [pagination, setPagination] = useState<PaginationState>(() => {
    const page = searchParams.get('page');
    const pageSize = searchParams.get('pageSize');
    return {
      pageIndex: page ? parseInt(page, 10) - 1 : DEFAULT_PAGE_INDEX,
      pageSize: pageSize ? parseInt(pageSize, 10) : DEFAULT_PAGE_SIZE,
    };
  });

  // Effect to update URL when pagination state changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString()); // Preserve other params
    params.set('page', (pagination.pageIndex + 1).toString());
    params.set('pageSize', pagination.pageSize.toString());
    const currentPageInUrl = searchParams.get('page');
    const currentSizeInUrl = searchParams.get('pageSize');
    if (
        (currentPageInUrl ? parseInt(currentPageInUrl,10) -1 : DEFAULT_PAGE_INDEX) !== pagination.pageIndex ||
        (currentSizeInUrl ? parseInt(currentSizeInUrl, 10) : DEFAULT_PAGE_SIZE) !== pagination.pageSize
    ) {
        router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    }
  }, [pagination, pathname, router, searchParams]);
  
  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Call the server action
      const response = await getWorksheetsAction({
        page: pagination.pageIndex + 1, // Action expects 1-based page
        pageSize: pagination.pageSize,
      });
      if (response.status === 'success' && response.data) {
        setWorksheetsData(response.data);
      } else if (response.status === 'error') { // Explicitly check for error status
        // Now response is narrowed to TApiError
        const errorMessage = typeof response.message === 'string' ? response.message : JSON.stringify(response.message);
        setError(errorMessage || 'Failed to fetch worksheets.');
        setWorksheetsData(null);
      } else {
        // Handle unexpected case, though theoretically covered by TTransformResponse
        setError('Received an unexpected response format.');
        setWorksheetsData(null);
      }
    } catch (e: any) {
      setError(e.message || 'An unexpected error occurred.');
      setWorksheetsData(null);
    } finally {
      setIsLoading(false);
    }
  }, [pagination.pageIndex, pagination.pageSize]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Handler for TablePagination's onPageChange (1-based page)
  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, pageIndex: newPage - 1 }));
  };

  // Handler for TablePagination's onRowsPerPageChange
  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setPagination((prev) => ({
      ...prev,
      pageSize: parseInt(event.target.value, 10),
      pageIndex: 0, // Reset to first page when page size changes
    }));
  };
  
  if (error) {
    // A more sophisticated error display could be used here
    return <div className="p-4 text-red-500">Error: {error}</div>;
  }

  return (
    <ListingTemplate
      header={<ManageWorksheetHeader />}
      table={
        <MobileOptimizedWorksheetTable
          worksheets={worksheetsData?.items || []}
          isLoading={isLoading}
          error={error}
          // Pass server-side pagination props directly
          currentPageBackend={worksheetsData?.meta.page || pagination.pageIndex + 1}
          totalPagesBackend={worksheetsData?.meta.totalPages || 0}
          totalItemsBackend={worksheetsData?.meta.total || 0}
          rowsPerPageBackend={pagination.pageSize}
          onBackendPageChange={handlePageChange} // This function now updates pagination state which triggers refetch
          onBackendRowsPerPageChange={handleRowsPerPageChange} // This function also updates pagination state
        />
      }
    />
  );
};
