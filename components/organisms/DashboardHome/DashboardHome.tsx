'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { 
  Building2, 
  Users, 
  BookOpen, 
  TrendingUp, 
  Calendar,
  Award,
  Settings,
  Sparkles
} from 'lucide-react';
import { getMySchool } from '@/actions/school.action';
import { ISchoolResponse } from '@/apis/schoolApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { EUserRole } from '@/config/enums/user';
import { 
  assessSchoolCustomization, 
  SchoolCustomizationLevel,
  getCustomizationLevelDescription 
} from '@/utils/schoolCustomization';
import { SchoolCustomizationPrompt } from '@/components/molecules/SchoolCustomizationPrompt/SchoolCustomizationPrompt';
import { SchoolCustomizationBanner } from '@/components/molecules/SchoolCustomizationBanner/SchoolCustomizationBanner';
import { cn } from '@/utils/cn';

interface DashboardHomeProps {
  className?: string;
}

export const DashboardHome: React.FC<DashboardHomeProps> = ({ className }) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const [schoolData, setSchoolData] = useState<ISchoolResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch school data
  const fetchSchoolData = useCallback(async () => {
    if (status !== 'authenticated' || !session?.user) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const response: TTransformResponse<ISchoolResponse | null> = await getMySchool();
      
      if (response.status === 'success') {
        setSchoolData(response.data);
      } else {
        const errorMsg = Array.isArray(response.message)
          ? response.message.join(', ')
          : String(response.message || 'Failed to fetch school data');
        setError(errorMsg);
      }
    } catch (err: any) {
      console.error('Error fetching school data:', err);
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [session, status]);

  useEffect(() => {
    fetchSchoolData();
  }, [fetchSchoolData]);

  const handleCustomizeClick = useCallback(() => {
    router.push('/school-customization');
  }, [router]);

  // Loading state
  if (status === 'loading' || isLoading) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <span className="loading loading-spinner loading-lg mb-4"></span>
            <p className="text-lg font-medium text-gray-700">Loading your dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="alert alert-error">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Error loading dashboard: {error}</span>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (status === 'unauthenticated' || !session?.user) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="alert alert-warning">
          <span>Please sign in to access your dashboard.</span>
        </div>
      </div>
    );
  }

  const user = session.user;
  const isIndependentTeacher = user.role === EUserRole.INDEPENDENT_TEACHER;

  // Assess school customization if we have school data
  const customizationAssessment = schoolData 
    ? assessSchoolCustomization(schoolData, user.name, user.email)
    : null;

  const renderWelcomeSection = () => {
    const currentHour = new Date().getHours();
    const greeting = currentHour < 12 ? 'Good morning' : currentHour < 18 ? 'Good afternoon' : 'Good evening';

    return (
      <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-base-content mb-2">
              {greeting}, {user.name}!
            </h1>
            {schoolData && (
              <p className="text-base-content/70 text-lg">
                Welcome to {schoolData.name}
              </p>
            )}
            {!schoolData && isIndependentTeacher && (
              <p className="text-base-content/70 text-lg">
                Ready to create your school?
              </p>
            )}
            {customizationAssessment && (
              <p className="text-base-content/60 text-sm mt-2">
                {getCustomizationLevelDescription(customizationAssessment.level)}
              </p>
            )}
          </div>

          {schoolData?.brand?.logo && (
            <div className="hidden sm:block">
              <Image
                src={schoolData.brand.logo}
                alt={`${schoolData.name} logo`}
                width={64}
                height={64}
                className="h-16 w-16 object-contain rounded-lg"
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderSchoolSetupBanner = () => {
    if (!isIndependentTeacher || schoolData) {
      return null;
    }

    return (
      <div className="alert alert-info mb-6">
        <div className="flex items-center gap-3">
          <Sparkles className="w-6 h-6 text-info" />
          <div className="flex-1">
            <h3 className="font-semibold text-lg">Complete Your Setup</h3>
            <p className="text-sm opacity-80">
              Create your school to unlock all features and start managing your educational institution effectively.
            </p>
          </div>
          <button
            className="btn btn-primary btn-sm"
            onClick={() => router.push('/my-school')}
          >
            Create School
          </button>
        </div>
      </div>
    );
  };

  const renderCustomizationSection = () => {
    if (!isIndependentTeacher || !schoolData || !customizationAssessment) {
      return null;
    }

    const isFullyCustomized = customizationAssessment.level === SchoolCustomizationLevel.FULLY_CUSTOMIZED;

    return (
      <div className="space-y-4 mb-6">
        {/* Banner for subtle ongoing prompts */}
        <SchoolCustomizationBanner
          assessment={customizationAssessment}
          schoolName={schoolData.name}
          onCustomizeClick={handleCustomizeClick}
        />

        {/* Main prompt card for basic/partial schools */}
        {!isFullyCustomized && (
          <SchoolCustomizationPrompt
            assessment={customizationAssessment}
            schoolName={schoolData.name}
            onCustomizeClick={handleCustomizeClick}
          />
        )}
      </div>
    );
  };

  const renderQuickStats = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="stat bg-base-100 rounded-lg shadow-sm">
          <div className="stat-figure text-primary">
            <Building2 className="w-8 h-8" />
          </div>
          <div className="stat-title">School Status</div>
          <div className="stat-value text-primary text-lg">Active</div>
          <div className="stat-desc">Ready for students</div>
        </div>

        <div className="stat bg-base-100 rounded-lg shadow-sm">
          <div className="stat-figure text-secondary">
            <Users className="w-8 h-8" />
          </div>
          <div className="stat-title">Students</div>
          <div className="stat-value text-secondary text-lg">0</div>
          <div className="stat-desc">Enrolled students</div>
        </div>

        <div className="stat bg-base-100 rounded-lg shadow-sm">
          <div className="stat-figure text-accent">
            <BookOpen className="w-8 h-8" />
          </div>
          <div className="stat-title">Worksheets</div>
          <div className="stat-value text-accent text-lg">0</div>
          <div className="stat-desc">Active Worksheets</div>
        </div>

        <div className="stat bg-base-100 rounded-lg shadow-sm">
          <div className="stat-figure text-info">
            <TrendingUp className="w-8 h-8" />
          </div>
          <div className="stat-title">Progress</div>
          <div className="stat-value text-info text-lg">
            {customizationAssessment?.score || 0}%
          </div>
          <div className="stat-desc">Setup complete</div>
        </div>
      </div>
    );
  };

  return (
    <div className={cn('space-y-6', className)}>
      {renderWelcomeSection()}
      {renderSchoolSetupBanner()}
      {renderCustomizationSection()}
      {renderQuickStats()}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {isIndependentTeacher && (
          <div className="card bg-base-100 shadow-sm">
            <div className="card-body">
              <h3 className="card-title text-lg">
                <Settings className="w-5 h-5" />
                {schoolData ? 'School Management' : 'Setup Your School'}
              </h3>
              <p className="text-base-content/70">
                {schoolData
                  ? 'Manage your school details, branding, and settings.'
                  : 'Create your school to unlock all features and start managing your educational institution.'
                }
              </p>
              <div className="card-actions justify-end">
                <button
                  className={cn(
                    "btn btn-sm",
                    schoolData ? "btn-primary" : "btn-primary btn-outline"
                  )}
                  onClick={() => router.push('/my-school')}
                >
                  {schoolData ? 'Manage School' : 'Create School'}
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="card bg-base-100 shadow-sm">
          <div className="card-body">
            <h3 className="card-title text-lg">
              <Calendar className="w-5 h-5" />
              Schedule
            </h3>
            <p className="text-base-content/70">
              View and manage your teaching schedule.
            </p>
            <div className="card-actions justify-end">
              <button className="btn btn-outline btn-sm">
                View Schedule
              </button>
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-sm">
          <div className="card-body">
            <h3 className="card-title text-lg">
              <Award className="w-5 h-5" />
              Achievements
            </h3>
            <p className="text-base-content/70">
              Track your teaching milestones and achievements.
            </p>
            <div className="card-actions justify-end">
              <button className="btn btn-outline btn-sm">
                View Progress
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardHome;
