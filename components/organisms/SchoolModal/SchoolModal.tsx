'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { requiredString } from '@/utils/zod';
import { handleCreateSchoolAction, handleUpdateSchoolAction } from '@/actions/school.action';
import { handleCreateBrandAction, handleUpdateBrandAction } from '@/actions/brand.action';
import { handleFileUploadAction } from '@/actions/file.action';
import { extractFileIdFromUrl, getFileRenderUrl } from '@/utils/fileUtils';
import { ISchoolResponse, IUpdateSchoolPayload } from '@/apis/schoolApi';
import { 
  Loader2, 
  School, 
  X, 
  Edit3, 
  Building2, 
  Palette, 

  MapPin,
  Phone,
  Mail,
  FileText,
  Info
} from 'lucide-react';
import { FormField } from '@/components/molecules/FormField/FormField';
import { FileUpload } from '@/components/molecules/FileUpload/FileUpload';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';

// Define the schema for the school form (used for both create and edit)
const schoolFormSchema = z.object({
  name: requiredString,
  address: requiredString,
  phoneNumber: requiredString.regex(/^\+?[0-9\s-()]*$/, 'Invalid phone number format'),
  registeredNumber: requiredString,
  email: requiredString.email('Please enter a valid email address'),
  // Brand fields - only for creation
  logo: z.any().optional(),
  logoUrl: z.string().optional(),
  image: z.any().optional(),
  imageUrl: z.string().optional(),
  color: z.string().optional(),
});

export type SchoolFormValues = z.infer<typeof schoolFormSchema>;

export interface SchoolModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (data: ISchoolResponse | { id: string; name: string }) => void;
  onError: (message: string) => void;
  schoolToEdit?: ISchoolResponse | null;
}

export const SchoolModal: React.FC<SchoolModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onError,
  schoolToEdit,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const isEditMode = !!schoolToEdit;
  const totalSteps = isEditMode ? 2 : 2; // Same steps for both modes

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset: resetSchoolForm,
    setValue,

    trigger
  } = useForm<SchoolFormValues>({
    resolver: zodResolver(schoolFormSchema),
    defaultValues: {
      name: '',
      address: '',
      phoneNumber: '',
      registeredNumber: '',
      email: '',
      logo: undefined,
      logoUrl: '',
      image: undefined,
      imageUrl: '',
      color: '#3B82F6',
    },
  });

  // Consolidated effect for form initialization and modal state management
  useEffect(() => {

    if (!isOpen) {
      // Reset everything when modal is closed
      return;
    }

    if (isEditMode && schoolToEdit) {

      // Convert existing URLs to the new render format
      const logoUrl = schoolToEdit.brand?.logo ? getFileRenderUrl(schoolToEdit.brand.logo) : '';
      const imageUrl = schoolToEdit.brand?.image ? getFileRenderUrl(schoolToEdit.brand.image) : '';

      // Reset form with existing data
      resetSchoolForm({
        name: schoolToEdit.name || '',
        address: schoolToEdit.address || '',
        phoneNumber: schoolToEdit.phoneNumber || '',
        registeredNumber: schoolToEdit.registeredNumber || '',
        email: schoolToEdit.email || '',
        color: schoolToEdit.brand?.color || '#3B82F6',
        logoUrl,
        imageUrl,
      });

      // Set previews using the converted URLs - only if they exist
      setLogoPreview(logoUrl || null);
      setImagePreview(imageUrl || null);
    } else {

      resetSchoolForm({
        name: '',
        address: '',
        phoneNumber: '',
        registeredNumber: '',
        email: '',
        logo: undefined,
        logoUrl: '',
        image: undefined,
        imageUrl: '',
        color: '#3B82F6',
      });

      // Clear previews for create mode
      setLogoPreview(null);
      setImagePreview(null);

      console.log('Create mode previews cleared');
    }

    // Reset step navigation
    setCurrentStep(1);
    setCompletedSteps([]);
    setFormError(null);
  }, [isOpen, isEditMode, schoolToEdit, resetSchoolForm]); // Only depend on essential props

  // Validate current step
  const validateStep = async (step: number): Promise<boolean> => {
    if (step === 1) {
      const result = await trigger(['name', 'email', 'phoneNumber', 'registeredNumber', 'address']);
      return result;
    }
    return true; // Step 2 (branding) is optional
  };

  // Handle step navigation
  const handleNextStep = async () => {
    const isValid = await validateStep(currentStep);
    if (isValid) {
      setCompletedSteps(prev => [...prev.filter(s => s !== currentStep), currentStep]);
      if (currentStep < totalSteps) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = async (step: number) => {
    if (step < currentStep || completedSteps.includes(step - 1)) {
      setCurrentStep(step);
    } else if (step === currentStep + 1) {
      await handleNextStep();
    }
  };

  // Handle file uploads with improved logging and state management
  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('handleLogoUpload called');
    const file = e.target.files?.[0];
    if (!file) {
      console.log('No file selected for logo upload');
      return;
    }

    if (!file.type.match('image.*')) {
      onError('Please select an image file for the logo');
      return;
    }
    if (file.size > 2 * 1024 * 1024) {
      onError('Logo file size should not exceed 2MB');
      return;
    }

    console.log('Processing logo file:', file.name);
    const reader = new FileReader();
    reader.onload = (ev) => {
      const dataUrl = ev.target?.result as string;
      console.log('Logo file read successfully, setting preview');

      // Set preview first
      setLogoPreview(dataUrl);
      // Then set form value
      setValue('logo', file);
      // Clear any existing logoUrl since we have a new file
      setValue('logoUrl', '');

      console.log('Logo preview and form values updated');
    };
    reader.readAsDataURL(file);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('handleImageUpload called');
    const file = e.target.files?.[0];
    if (!file) {
      console.log('No file selected for image upload');
      return;
    }

    if (!file.type.match('image.*')) {
      onError('Please select an image file for the brand image');
      return;
    }
    if (file.size > 2 * 1024 * 1024) {
      onError('Image file size should not exceed 2MB');
      return;
    }

    console.log('Processing image file:', file.name);
    const reader = new FileReader();
    reader.onload = (ev) => {
      const dataUrl = ev.target?.result as string;
      console.log('Image file read successfully, setting preview');

      // Set preview first
      setImagePreview(dataUrl);
      // Then set form value
      setValue('image', file);
      // Clear any existing imageUrl since we have a new file
      setValue('imageUrl', '');

      console.log('Image preview and form values updated');
    };
    reader.readAsDataURL(file);
  };

  const handleClearLogo = () => {
    console.log('Clearing logo preview and form values');
    setLogoPreview(null);
    setValue('logo', undefined);
    setValue('logoUrl', '');
  };

  const handleClearImage = () => {
    console.log('Clearing image preview and form values');
    setImagePreview(null);
    setValue('image', undefined);
    setValue('imageUrl', '');
  };

  const onSubmitSchool = async (data: SchoolFormValues) => {
    setIsSubmitting(true);
    setFormError(null);

    try {
      if (isEditMode && schoolToEdit) {
        // Update existing school and brand
        const updatePayload: IUpdateSchoolPayload = {
          name: data.name,
          address: data.address,
          phoneNumber: data.phoneNumber,
          registeredNumber: data.registeredNumber,
          email: data.email,
        };

        // Handle brand updates if there are changes
        let brandUpdateNeeded = false;
        let logoFileId = '';
        let imageFileId = '';

        // Upload new logo if provided
        if (data.logo && data.logo instanceof File) {
          const logoUploadResponse = await handleFileUploadAction(data.logo, 'School logo', 'logo');
          if (logoUploadResponse.status === 'success' && logoUploadResponse.data) {
            logoFileId = logoUploadResponse.data.fileId;
            brandUpdateNeeded = true;
          } else if (logoUploadResponse.status === 'error') {
            let errorMsg = 'Failed to upload logo.';
            if (typeof logoUploadResponse.message === 'string') {
              errorMsg = logoUploadResponse.message;
            } else if (Array.isArray(logoUploadResponse.message)) {
              errorMsg = logoUploadResponse.message.map(e => `${e.field}: ${e.constraints}`).join(', ');
            }
            setFormError('Failed to upload logo: ' + errorMsg);
            onError('Failed to upload logo: ' + errorMsg);
            setIsSubmitting(false);
            return;
          }
        } else if (schoolToEdit.brand?.logo) {
          // Keep existing logo file ID if no new upload
          logoFileId = extractFileIdFromUrl(schoolToEdit.brand.logo) || '';
        }

        // Upload new image if provided
        if (data.image && data.image instanceof File) {
          const imageUploadResponse = await handleFileUploadAction(data.image, 'School brand image', 'image');
          if (imageUploadResponse.status === 'success' && imageUploadResponse.data) {
            imageFileId = imageUploadResponse.data.fileId;
            brandUpdateNeeded = true;
          } else if (imageUploadResponse.status === 'error') {
            let errorMsg = 'Failed to upload image.';
            if (typeof imageUploadResponse.message === 'string') {
              errorMsg = imageUploadResponse.message;
            } else if (Array.isArray(imageUploadResponse.message)) {
              errorMsg = imageUploadResponse.message.map(e => `${e.field}: ${e.constraints}`).join(', ');
            }
            setFormError('Failed to upload image: ' + errorMsg);
            onError('Failed to upload image: ' + errorMsg);
            setIsSubmitting(false);
            return;
          }
        } else if (schoolToEdit.brand?.image) {
          // Keep existing image file ID if no new upload
          imageFileId = extractFileIdFromUrl(schoolToEdit.brand.image) || '';
        }

        // Check if color changed
        if (data.color !== schoolToEdit.brand?.color) {
          brandUpdateNeeded = true;
        }

        // Update brand if needed
        if (brandUpdateNeeded && schoolToEdit.brand?.id) {
          const brandUpdatePayload = {
            logo: logoFileId,
            color: data.color,
            image: imageFileId,
          };
          const brandResponse = await handleUpdateBrandAction(schoolToEdit.brand.id, brandUpdatePayload);
          if (brandResponse.status === 'error') {
            let errorMsg = 'Failed to update brand.';
            if (typeof brandResponse.message === 'string') {
              errorMsg = brandResponse.message;
            } else if (Array.isArray(brandResponse.message)) {
              errorMsg = brandResponse.message.map(e => `${e.field}: ${e.constraints}`).join(', ');
            }
            setFormError(errorMsg);
            onError(errorMsg);
            setIsSubmitting(false);
            return;
          }
        }

        // Update school
        const response = await handleUpdateSchoolAction(schoolToEdit.id, updatePayload);
        if (response.status === 'success' && response.data) {
          onSuccess(response.data);
          handleCloseModal();
        } else if (response.status === 'error') {
          let errorMsg = 'Failed to update school.';
          if (typeof response.message === 'string') {
            errorMsg = response.message;
          } else if (Array.isArray(response.message)) {
            errorMsg = response.message.map(e => `${e.field}: ${e.constraints}`).join(', ');
          }
          setFormError(errorMsg);
          onError(errorMsg);
        }
      } else {
        // Create new school
        let logoFileId = '';
        if (data.logo && data.logo instanceof File) {
          const logoUploadResponse = await handleFileUploadAction(data.logo, 'School logo', 'logo');
          if (logoUploadResponse.status === 'success' && logoUploadResponse.data) {
            logoFileId = logoUploadResponse.data.fileId;
          } else if (logoUploadResponse.status === 'error') {
            let errorMsg = 'Failed to upload logo.';
            if (typeof logoUploadResponse.message === 'string') {
              errorMsg = logoUploadResponse.message;
            } else if (Array.isArray(logoUploadResponse.message)) {
              errorMsg = logoUploadResponse.message.map(e => `${e.field}: ${e.constraints}`).join(', ');
            }
            setFormError('Failed to upload logo: ' + errorMsg);
            onError('Failed to upload logo: ' + errorMsg);
            setIsSubmitting(false);
            return;
          }
        }

        let imageFileId = '';
        if (data.image && data.image instanceof File) {
          const imageUploadResponse = await handleFileUploadAction(data.image, 'School brand image', 'image');
          if (imageUploadResponse.status === 'success' && imageUploadResponse.data) {
            imageFileId = imageUploadResponse.data.fileId;
          } else if (imageUploadResponse.status === 'error') {
            let errorMsg = 'Failed to upload image.';
            if (typeof imageUploadResponse.message === 'string') {
              errorMsg = imageUploadResponse.message;
            } else if (Array.isArray(imageUploadResponse.message)) {
              errorMsg = imageUploadResponse.message.map(e => `${e.field}: ${e.constraints}`).join(', ');
            }
            setFormError('Failed to upload image: ' + errorMsg);
            onError('Failed to upload image: ' + errorMsg);
            setIsSubmitting(false);
            return;
          }
        }

        const brandPayload = { logo: logoFileId, color: data.color, image: imageFileId };
        const brandResponse = await handleCreateBrandAction(brandPayload);

        if (brandResponse.status === 'success' && brandResponse.data) {
          const schoolPayload = {
            name: data.name,
            address: data.address,
            phoneNumber: data.phoneNumber,
            registeredNumber: data.registeredNumber,
            email: data.email,
            brandId: brandResponse.data.id,
          };
          const schoolResponse = await handleCreateSchoolAction(schoolPayload);
          if (schoolResponse.status === 'success' && schoolResponse.data) {
            onSuccess({ id: schoolResponse.data.id, name: schoolResponse.data.name });
            handleCloseModal();
          } else if (schoolResponse.status === 'error') {
            let errorMsg = 'Failed to create school.';
            if (typeof schoolResponse.message === 'string') {
              errorMsg = schoolResponse.message;
            } else if (Array.isArray(schoolResponse.message)) {
              errorMsg = schoolResponse.message.map(e => `${e.field}: ${e.constraints}`).join(', ');
            }
            setFormError(errorMsg);
            onError(errorMsg);
          }
        } else if (brandResponse.status === 'error') {
          let errorMsg = 'Failed to create brand.';
          if (typeof brandResponse.message === 'string') {
            errorMsg = brandResponse.message;
          } else if (Array.isArray(brandResponse.message)) {
            errorMsg = brandResponse.message.map(e => `${e.field}: ${e.constraints}`).join(', ');
          }
          setFormError(errorMsg);
          onError(errorMsg);
        }
      }
    } catch (error: any) {
      const genericError = 'An unexpected error occurred.';
      setFormError(genericError);
      onError(error.message || genericError);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCloseModal = () => {
    resetSchoolForm();
    setLogoPreview(null);
    setImagePreview(null);
    setFormError(null);
    setCurrentStep(1);
    setCompletedSteps([]);
    onClose();
  };

  if (!isOpen) return null;

  const modalTitle = isEditMode ? 'Edit School Information' : 'Create New School';
  const submitButtonText = isEditMode ? 'Save Changes' : 'Create School';
  const submittingButtonText = isEditMode ? 'Saving...' : 'Creating...';

  const steps = [
    {
      number: 1,
      title: 'School Details',
      description: 'Basic information about the school',
      icon: <Building2 size={20} />,
    },
    {
      number: 2,
      title: 'Brand Identity',
      description: 'Visual branding and customization',
      icon: <Palette size={20} />,
    },
  ];

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4" style={{ zIndex: 9999 }}>
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4"> {/* Updated to blue-600 */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              {isEditMode ? <Edit3 size={20} /> : <School size={20} />} {/* Reduced icon size */}
              <h2 className="text-lg font-semibold">{modalTitle}</h2> {/* Simplified title, removed subtitle */}
            </div>
            <button
              onClick={handleCloseModal}
              className="rounded-full p-1.5 hover:bg-slate-600 transition-colors duration-200" /* Adjusted padding and hover */
              aria-label="Close"
            >
              <X size={20} className="text-white" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-100px)]"> {/* Adjusted max-h due to smaller header */}
          {formError && (
            <div className="mb-4"> {/* Reduced margin for error message */}
              <AlertMessage type="error" message={formError} />
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmitSchool)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6"> {/* Main two-column grid */}
              {/* Left Column: School Details */}
              <div className="space-y-4"> {/* Reduced space-y from 6 to 4 */}
                <div>
                  <h3 className="text-md font-semibold text-gray-800 mb-1">School Details</h3> {/* Simplified title */}
                  <p className="text-xs text-gray-500 mb-3">Basic information about the school</p>
                </div>
                
                <FormField label="School Name" error={errors.name?.message} icon={<School size={16} className="text-gray-400" />}>
                  <input 
                    type="text" 
                    placeholder="Enter school name" 
                    className={`w-full pl-10 pr-3 py-2.5 border ${errors.name ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors text-sm`} /* Reduced padding, rounded-md, ring-1, text-sm */
                    {...register('name')} 
                  />
                </FormField>

                <FormField label="School Address" error={errors.address?.message} icon={<MapPin size={16} className="text-gray-400" />}>
                  <input 
                    type="text"
                    placeholder="Enter complete school address" 
                    className={`w-full pl-10 pr-3 py-2.5 border ${errors.address ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors text-sm`} /* Changed to input, removed rows and resize-none */
                    {...register('address')} 
                  />
                </FormField>

                <FormField label="Contact Email" error={errors.email?.message} icon={<Mail size={16} className="text-gray-400" />}>
                  <input 
                    type="email" 
                    placeholder="<EMAIL>" 
                    className={`w-full pl-10 pr-3 py-2.5 border ${errors.email ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors text-sm`} /* Reduced padding, rounded-md, ring-1, text-sm */
                    {...register('email')} 
                  />
                </FormField>

                <FormField label="Phone Number" error={errors.phoneNumber?.message} icon={<Phone size={16} className="text-gray-400" />}>
                  <input 
                    type="text" 
                    placeholder="+****************" 
                    className={`w-full pl-10 pr-3 py-2.5 border ${errors.phoneNumber ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors text-sm`} /* Reduced padding, rounded-md, ring-1, text-sm */
                    {...register('phoneNumber')} 
                  />
                </FormField>

                <FormField label="Registration Number" error={errors.registeredNumber?.message} icon={<FileText size={16} className="text-gray-400" />}>
                  <input 
                    type="text" 
                    placeholder="REG123456789" 
                    className={`w-full pl-10 pr-3 py-2.5 border ${errors.registeredNumber ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'} rounded-md shadow-sm focus:outline-none focus:ring-1 transition-colors text-sm`} /* Reduced padding, rounded-md, ring-1, text-sm */
                    {...register('registeredNumber')} 
                  />
                </FormField>
              </div>

              {/* Right Column: Brand Configuration */}
              <div className="space-y-4"> {/* Reduced space-y from 6 to 4 */}
                 <div>
                  <h3 className="text-md font-semibold text-gray-800 mb-1">Brand Configuration</h3> {/* Simplified title */}
                  <p className="text-xs text-gray-500 mb-3">Visual identity for the school</p>
                </div>

                <FormField label="School Logo" error={errors.logo?.message as string}>
                  <FileUpload 
                    id="logo-upload" 
                    preview={logoPreview} 
                    onUpload={handleLogoUpload} 
                    onClear={handleClearLogo} 
                    disabled={false}
                    label="Click to upload or drag and drop" // Matched screenshot text
                  />
                </FormField>

                <FormField label="Brand Image" error={errors.image?.message as string}>
                  <FileUpload 
                    id="image-upload" 
                    preview={imagePreview} 
                    onUpload={handleImageUpload} 
                    onClear={handleClearImage} 
                    disabled={false}
                    label="Click to upload or drag and drop" // Matched screenshot text
                  />
                </FormField>
                
                <FormField label="Brand Color" error={errors.color?.message}>
                  <div className="flex items-center gap-3"> {/* Reduced gap */}
                    <input 
                      type="color" 
                      className="h-10 w-16 rounded-md border border-gray-300 cursor-pointer shadow-sm" /* Reduced size */
                      {...register('color')} 
                    />
                    <p className="text-xs text-gray-500">Main color for school branding</p> {/* Simplified text */}
                  </div>
                </FormField>

                {/* Note from screenshot - simplified */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-xs text-yellow-700">
                  <div className="flex items-start">
                    <Info size={16} className="mr-2 mt-0.5 flex-shrink-0 text-yellow-600" />
                    <span>School branding will be applied to all materials and interfaces associated with this school.</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer Buttons */}
            <div className="flex justify-end items-center pt-6 mt-6 border-t border-gray-200 space-x-3"> {/* justify-end and space-x-3 */}
              <button
                type="button"
                onClick={handleCloseModal}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors" /* Adjusted style */
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors min-w-[120px]" /* Updated to blue-600 */
              >
                {isSubmitting ? (
                  <>
                    <Loader2 size={16} className="animate-spin mr-2" />
                    {submittingButtonText}
                  </>
                ) : (
                  submitButtonText
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
