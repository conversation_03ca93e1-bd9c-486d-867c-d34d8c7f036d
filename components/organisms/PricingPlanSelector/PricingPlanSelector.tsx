'use client';

import * as React from 'react';
import { PricingCard, PackageData } from '@/components/molecules/PricingCard';
import { PricingToggle } from '@/components/atoms/PricingToggle/PricingToggle';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { handleCreateSubscriptionSessionAction } from '@/actions/package.action';

const plans = [
  {
    id: 'basic-plan', // TODO: Replace with actual package ID from API
    name: 'Basic',
    description: 'Perfect for small teams getting started',
    monthlyPrice: 10,
    yearlyPrice: 100,
    features: ['5 Users', '10GB Storage', 'Email Support'],
  },
  {
    id: 'pro-plan', // TODO: Replace with actual package ID from API
    name: 'Pro',
    description: 'Ideal for growing teams and businesses',
    monthlyPrice: 25,
    yearlyPrice: 250,
    features: ['20 Users', '50GB Storage', 'Priority Email Support'],
    popular: true,
  },
  {
    id: 'enterprise-plan', // TODO: Replace with actual package ID from API
    name: 'Enterprise',
    description: 'Advanced features for large organizations',
    monthlyPrice: 50,
    yearlyPrice: 500,
    features: ['Unlimited Users', '200GB Storage', 'Phone & Email Support'],
  },
];

interface PricingPlanSelectorProps {
  isManagement?: boolean;
}

export const PricingPlanSelector: React.FC<PricingPlanSelectorProps> = ({ isManagement = false }) => {
  const [isYearly, setIsYearly] = React.useState(false);
  const [subscribingPackageId, setSubscribingPackageId] = React.useState<string | null>(null);
  const [error, setError] = React.useState<string | null>(null);
  const { data: session } = useSession();
  const router = useRouter();
  const userPlanName = session?.user.subscription?.packageName;

  const handleManageSubscription = () => {
    // Redirect to a subscription management portal, e.g., Stripe Customer Portal
    // This is a placeholder URL.
    router.push('/api/stripe/create-portal-session');
  };

  // Format error message helper
  const formatErrorMessage = (message: string): string => {
    if (message.includes('User not authenticated')) {
      return 'Please sign in to subscribe to a plan.';
    }
    if (message.includes('Package ID is required')) {
      return 'Invalid package selected. Please try again.';
    }
    return message || 'An unexpected error occurred. Please try again.';
  };

  // Handle subscription - same logic as pricing page
  const handleSubscribe = async (packageData: PackageData) => {
    if (!packageData.id) {
      setError('Invalid package selected. Please try again.');
      return;
    }

    try {
      setSubscribingPackageId(packageData.id);
      setError(null);

      // Get the current URL for success/cancel redirects
      const currentOrigin = window.location.origin;

      // Create subscription session payload
      const payload = {
        packageId: packageData.id,
        successUrl: `${currentOrigin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${currentOrigin}/pricing`,
      };

      const response = await handleCreateSubscriptionSessionAction(payload);

      if (response.status === 'error') {
        setError(formatErrorMessage(response.message || 'An error occurred'));
        return;
      }

      // Redirect to Stripe checkout
      if (response.data) {
        window.location.href = response.data;
      } else {
        setError('Failed to create checkout session. Please try again.');
      }
    } catch (err) {
      console.error('Subscription error:', err);
      setError('Failed to start subscription process. Please try again.');
    } finally {
      setSubscribingPackageId(null);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 sm:py-8 max-w-7xl">
      {/* Header */}
      <div className="text-center mb-8 sm:mb-12">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-text-primary mb-4">
          {isManagement ? 'My Subscription' : 'Our Pricing'}
        </h2>
      </div>

      {/* Error Display */}
      {error && (
        <div className="max-w-md mx-auto mb-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600 text-sm text-center">{error}</p>
          </div>
        </div>
      )}

      {/* Toggle */}
      <div className="flex justify-center mb-8 sm:mb-12">
        <PricingToggle onToggle={setIsYearly} />
      </div>

      {/* Pricing Cards Grid - Optimized for tablet */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-3 gap-4 sm:gap-5 md:gap-6 lg:gap-8 items-stretch justify-items-center max-w-6xl mx-auto">
        {plans.map((plan, index) => (
          <div key={index} className="w-full max-w-sm sm:max-w-none md:max-w-sm lg:max-w-none">
            <PricingCard
              package={{
                id: plan.id,
                name: plan.name,
                description: plan.description,
                price: isYearly ? plan.yearlyPrice : plan.monthlyPrice,
                features: plan.features,
                currency: 'SGD',
                interval: isYearly ? 'year' : 'month',
                popular: plan.popular,
              }}
              onSubscribe={handleSubscribe}
              onManageSubscription={handleManageSubscription}
              isCurrentPlan={isManagement && userPlanName === plan.name}
              isLoading={subscribingPackageId === plan.id}
              disabled={subscribingPackageId !== null}
              className="w-full h-full"
            />
          </div>
        ))}
      </div>
    </div>
  );
};