'use client';

import React, { useState } from 'react';
import { handleDeleteSchoolAction } from '@/actions/school.action';
import { Loader2, AlertCircle, School as SchoolIcon, Trash, X } from 'lucide-react';
import { ISchoolResponse } from '@/apis/schoolApi'; // Assuming ISchoolResponse is the correct type
import { Button } from '@/components/atoms/Button/Button'; // Using the project's Button component
import { cn } from '@/utils/cn';

export interface DeleteSchoolModalProps {
  isOpen: boolean;
  onClose: () => void;
  school: ISchoolResponse | null; // Changed from user to school
  onSuccess?: () => void;
}

export const DeleteSchoolModal: React.FC<DeleteSchoolModalProps> = ({
  isOpen,
  onClose,
  school,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!school) return; // Ensure school is not null

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await handleDeleteSchoolAction(school.id);

      if (response.status === 'success') {
        setSuccess('School deleted successfully!');
        setTimeout(() => {
          if (onSuccess) {
            onSuccess();
          }
          onClose(); // Close modal after success and timeout
        }, 2000);
      } else {
        // Ensure message is a string
        const errorMessage = Array.isArray(response.message) ? response.message.join(', ') : String(response.message || 'Failed to delete school.');
        setError(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen || !school) return null; // Also check for school object

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn" style={{ zIndex: 9999 }}>
      <div className="bg-white rounded-xl shadow-2xl p-0 w-full max-w-sm overflow-hidden transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="bg-gray-800 text-white p-4 flex justify-between items-center">
          <h2 className="text-lg font-bold flex items-center gap-2">
            <Trash size={18} />
            Delete
          </h2>
          <button 
            onClick={onClose}
            className="rounded-full p-1.5 bg-white/20 hover:bg-white/30 transition-colors duration-200"
            aria-label="Close"
          >
            <X size={16} className="text-white" />
          </button>
        </div>

        <div className="p-4">
          {/* Status messages */}
          {error && (
            <div className="mb-3 p-2 bg-red-50 border-l-4 border-red-500 rounded-r-md flex items-start gap-2">
              <div className="p-1 bg-red-100 rounded-full text-red-600">
                <AlertCircle size={16} />
              </div>
              <span className="text-red-700 text-xs">{error}</span>
            </div>
          )}

          {success && (
            <div className="mb-3 p-2 bg-green-50 border-l-4 border-green-500 rounded-r-md flex items-start gap-2">
              <div className="p-1 bg-green-100 rounded-full text-green-600">
                <AlertCircle size={16} className="text-green-600" />
              </div>
              <span className="text-green-700 text-xs">{success}</span>
            </div>
          )}

          {/* Warning message */}
          <div className="mb-3 p-2 bg-background-subtle border-l-4 border-gray-300 rounded-r-md flex items-center gap-2">
            <AlertCircle size={16} className="text-text-secondary" />
            <span className="text-text-primary text-xs">This action cannot be undone.</span>
          </div>

          {/* School information card */}
          <div className="bg-background-subtle rounded-lg p-3 mb-4 border border-gray-200 flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-link-default flex items-center justify-center text-background-default shadow-sm"> {/* Changed icon and color for school */}
              <SchoolIcon size={20} />
            </div>
            <div>
              <h2 className="text-sm font-semibold text-text-primary">{school.name}</h2>
              <p className="text-xs text-text-secondary">ID: {school.id}</p>
              {school.email && <p className="text-xs text-text-secondary">{school.email}</p>}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex justify-end gap-2">
            <Button
              variant="outline" // Assuming Button component has 'outline' variant
              onClick={onClose}
              className="px-3 py-1.5 rounded-lg border border-gray-300 text-gray-700 text-sm hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </Button>
            <Button
              variant="error" // Assuming Button component has 'error' variant for destructive actions
                onClick={handleDelete}
                className={cn(
                "px-3 py-1.5 rounded-lg text-white text-sm transition-all duration-200 flex items-center gap-1.5",
                  isSubmitting ? "opacity-70 cursor-not-allowed" : "shadow-sm hover:shadow-md"
                )}
                disabled={isSubmitting}
              >
              {isSubmitting ? (
                <>
                  Deleting...
                </>
              ) : (
                <>
                  Delete
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
