'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { DeleteUserModal } from '@/components/molecules/DeleteUserModal';
import { EUserRole } from '@/config/enums/user';
import { ColumnDef } from '@tanstack/react-table';
import {
  Loader2,
  UserCircle,
  School,
  Edit,
  Trash2,
  Mail,
  X,
  MoreHorizontal,
  AlertCircle,
  Filter,
  Users, ChevronDown, Download, ChevronUp, Search,
} from 'lucide-react';

// Import custom components
import { UserTableHeader } from '@/components/molecules/UserTableHeader/UserTableHeader';
import { UserTableFilterPanel } from '@/components/molecules/UserTableFilterPanel/UserTableFilterPanel';
import { UserTableBulkActions } from '@/components/molecules/UserTableBulkActions/UserTableBulkActions';
import { TablePagination } from '@/components/molecules/TablePagination/TablePagination';
import { AnimationStyles } from '@/components/atoms/AnimationStyles/AnimationStyles';
import { StatusBadge } from '@/components/atoms/StatusBadge/StatusBadge';
import { RoleBadge } from '@/components/atoms/RoleBadge/RoleBadge';
// import { UserTableRow } from '@/components/molecules/UserTableRow/UserTableRow'; // Not used directly
// import TableCommon from '@/components/molecules/CustomTable/CustomTable'; // Not used directly

export interface UsersTableProps {
  users: Array<{
    id: string;
    name: string;
    email: string;
    role: EUserRole;
    schoolId?: string | null;
    status?: 'active' | 'inactive' | 'pending' | 'suspended';
    lastActivity?: string;
  }>;
  error: string | null;
  isLoading?: boolean;
  tableTitle?: string;
  entityName?: string;
  entityNamePlural?: string;
  accountType?: string;
  editPath?: string;
  createPath?: string;
  hideRoleColumn?: boolean;
  hideSchoolColumn?: boolean;
  hideRoleFilter?: boolean; // Add new prop for hiding role filter
  schools?: Array<{
    id: string;
    name: string;
  }>;
}

export const UsersTable: React.FC<UsersTableProps> = ({ 
  users, 
  error, 
  isLoading = false,
  tableTitle = "All Users",
  entityName = "user",
  entityNamePlural = "users",
  accountType = "User account",
  editPath = "/users-management/edit/",
  createPath = "/users-management/create",
  hideRoleColumn = false,
  hideSchoolColumn = false,
  hideRoleFilter = false, // Add default value for new prop
  schools = []
}) => {
  // Modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<{
    id: string;
    name: string;
    email: string;
    role: EUserRole;
    schoolId?: string | null;
    status?: 'active' | 'inactive' | 'pending' | 'suspended';
    lastActivity?: string;
  } | null>(null);

  // Table state
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [filters, setFilters] = useState<{
    role: EUserRole | 'all';
    status: 'active' | 'inactive' | 'pending' | 'suspended' | 'all';
  }>({
    role: 'all',
    status: 'all'
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on search
  };

  // Apply filters, search, and sorting to users
  const filteredUsers = useMemo(() => {
    let result = [...users];
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      result = result.filter(user =>
        user.name.toLowerCase().includes(lowerSearchTerm) ||
        user.email.toLowerCase().includes(lowerSearchTerm) ||
        user.id.toLowerCase().includes(lowerSearchTerm)
      );
    }
    if (filters.role !== 'all') {
      result = result.filter(user => user.role === filters.role);
    }
    if (filters.status !== 'all') {
      result = result.filter(user => user.status === filters.status);
    }
    if (sortColumn) {
      result = [...result].sort((a, b) => {
        const aValue = a[sortColumn as keyof typeof a];
        const bValue = b[sortColumn as keyof typeof b];
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortDirection === 'asc'
            ? aValue - bValue
            : bValue - aValue;
        }
        return 0;
      });
    }
    return result;
  }, [users, searchTerm, filters, sortColumn, sortDirection]);

  const handleOpenDeleteModal = useCallback((user: typeof users[0]) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  }, [setSelectedUser, setIsDeleteModalOpen]);

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setSelectedUser(null); // Clear selected user on close
  };

  // Sorting handlers
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Selection handlers
  const handleSelectAll = useCallback(() => {
    if (isAllSelected) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map(user => user.id));
    }
    setIsAllSelected(!isAllSelected);
  }, [isAllSelected, filteredUsers, setIsAllSelected, setSelectedUsers]);

  const handleSelectUser = useCallback((userId: string) => {
    setSelectedUsers(prevSelected => {
      const newSelected = prevSelected.includes(userId)
        ? prevSelected.filter(id => id !== userId)
        : [...prevSelected, userId];

      setIsAllSelected(newSelected.length === filteredUsers.length && filteredUsers.length > 0);
      return newSelected;
    });
  }, [filteredUsers, setIsAllSelected, setSelectedUsers]);


  // Bulk action handlers
  const handleBulkDelete = () => {
    alert(`Deleting ${selectedUsers.length} users`);
    setSelectedUsers([]);
    setIsAllSelected(false);
  };

  const handleBulkEmail = () => {
    alert(`Emailing ${selectedUsers.length} users`);
  };

  // Filter handlers
  const toggleFilterPanel = () => {
    setIsFilterPanelOpen(!isFilterPanelOpen);
  };

  const handleFilterChange = (filterName: string, value: any) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      [filterName]: value
    }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({
      role: 'all',
      status: 'all'
    });
    setSearchTerm(''); // Also clear search term when clearing filters
    setCurrentPage(1);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setRowsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Define column definitions for the table
  const columns = useMemo<ColumnDef<typeof users[0]>[]>(() => {
    // Start with base columns
    const baseColumns: ColumnDef<typeof users[0]>[] = [
      {
        id: 'select',
        header: () => (
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={isAllSelected}
              onChange={handleSelectAll}
              className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              aria-label={`Select all ${entityNamePlural}`}
              disabled={filteredUsers.length === 0} // Disable if no users to select
            />
          </div>
        ),
        cell: ({ row }) => (
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={selectedUsers.includes(row.original.id)}
              onChange={() => handleSelectUser(row.original.id)}
              className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              aria-label={`Select ${row.original.name}`}
            />
          </div>
        ),
        // theadColClass: 'w-10' // This prop is not standard for TanStack Table ColumnDef
      },
      {
        accessorKey: 'name', // Use accessorKey for direct data access
        header: entityName.charAt(0).toUpperCase() + entityName.slice(1),
        cell: ({ row }) => (
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full flex items-center justify-center bg-gray-600 text-white">
              <UserCircle size={28} />
            </div>
            <div>
              <span className="font-medium text-gray-800 block text-base">{row.original.name}</span>
              <span className="text-xs text-gray-500 mt-0.5 inline-block">ID: {row.original.id.substring(0, 8)}...</span>
            </div>
          </div>
        ),
        // theadColClass: 'w-1/4'
      },
      {
        accessorKey: 'email',
        header: 'Email',
        cell: ({ row }) => (
          <div className="flex flex-col">
            <span className="text-gray-700 font-medium">{row.original.email}</span>
            <span className="text-xs text-gray-500 mt-0.5">{accountType}</span>
          </div>
        ),
        // theadColClass: 'w-1/4'
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => <StatusBadge status={row.original.status || 'active'} />,
        // theadColClass: 'w-1/6'
      },
      {
        accessorKey: 'schoolId',
        header: 'School',
        cell: ({ row }) => {
          if (!row.original.schoolId) {
            return <span className="text-gray-400 italic px-3 py-1.5 bg-gray-50 rounded-full text-xs">Not assigned</span>;
          }

          // Find the school name if schools data is provided
          const schoolName = schools.find(school => school.id === row.original.schoolId)?.name;

          return (
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                <School size={16} className="text-gray-600" />
              </div>
              <div className="flex flex-col">
                <span className="text-gray-700 font-medium">
                  {schoolName || row.original.schoolId.substring(0, 8) + '...'}
                </span>
                <span className="text-xs text-gray-500">
                  {schoolName ? 'School Name' : 'School ID'}
                </span>
              </div>
            </div>
          );
        },
        // theadColClass: 'w-1/6'
      },
      {
        id: 'actions',
        header: () => <div className="text-right">Actions</div>, // Header can be a JSX element
        cell: ({ row }) => (
          <div className="flex items-center gap-2 justify-end">
            <a
              href={`${editPath}${row.original.id}`}
              className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 flex items-center gap-1.5"
              title={`Edit ${entityName.charAt(0).toUpperCase() + entityName.slice(1)}`}
            >
              <Edit size={18} />
            </a>
            <button
              onClick={() => handleOpenDeleteModal(row.original)}
              className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 flex items-center gap-1.5"
              title={`Delete ${entityName.charAt(0).toUpperCase() + entityName.slice(1)}`}
            >
              <Trash2 size={18} />
            </button>
          </div>
        ),
        // theadColClass: 'w-1/6 text-right'
      }
    ];

    // Add role column if not hidden
    if (!hideRoleColumn) {
      // Find the index to insert the role column (after email)
      const emailIndex = baseColumns.findIndex(col => (col as any).accessorKey === 'email');
      const insertIndex = emailIndex !== -1 ? emailIndex + 1 : 3; // Default to position 3 if email not found

      baseColumns.splice(insertIndex, 0, {
        accessorKey: 'role',
        header: 'Role',
        cell: ({ row }) => <RoleBadge role={row.original.role} />,
        // theadColClass: 'w-1/6'
      });
    }

    // Filter out school column if hideSchoolColumn is true
    if (hideSchoolColumn) {
      return baseColumns.filter(col => (col as any).accessorKey !== 'schoolId');
    }

    return baseColumns;
  }, [isAllSelected, handleSelectAll, selectedUsers, handleSelectUser, handleOpenDeleteModal, filteredUsers.length, hideRoleColumn, hideSchoolColumn, schools, entityName, entityNamePlural, accountType, editPath]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredUsers.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + rowsPerPage);

  // Check if there are any active filters
  const hasActiveFilters = filters.role !== 'all' || filters.status !== 'all' || searchTerm !== '';

  return (
    <div className="overflow-hidden rounded-[0.5rem] border border-gray-200 shadow-sm bg-white">
      <style dangerouslySetInnerHTML={{ __html: AnimationStyles }} />
      <UserTableHeader
        title={tableTitle}
        subtitle={isLoading ? `Loading ${entityNamePlural}...` : `${filteredUsers.length} ${filteredUsers.length === 1 ? entityName : entityNamePlural} found`}
        searchTerm={searchTerm}
        onSearchChange={handleSearchChange}
        onClearSearch={clearFilters} // Changed to clearFilters for consistency
        onToggleFilterPanel={toggleFilterPanel}
        isFilterPanelOpen={isFilterPanelOpen}
        hasActiveFilters={hasActiveFilters}
        activeFiltersCount={(filters.role !== 'all' ? 1 : 0) + (filters.status !== 'all' ? 1 : 0)}
        onExport={() => alert('Exporting users...')}
      />

      {isFilterPanelOpen && (
        <UserTableFilterPanel
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          hideRoleFilter={hideRoleFilter} // Pass prop to filter panel
        />
      )}

      {selectedUsers.length > 0 && (
        <UserTableBulkActions
          selectedCount={selectedUsers.length}
          onClearSelection={() => {
            setSelectedUsers([]);
            setIsAllSelected(false);
          }}
          onBulkEmail={handleBulkEmail}
          onBulkDelete={handleBulkDelete}
        />
      )}

      <div className="overflow-x-auto scrollbar">
        <table className="w-full">
          <thead className="sticky top-0 z-10 overflow-y-hidden bg-gray-100"> {/* Changed from bg-dark-gray-300 */}
            <tr>
              {columns.map(column => (
                <th 
                  key={column.id || (column as any).accessorKey} 
                  className={`py-3 px-4 text-left ${(column as any).theadColClass || ''}`} // Apply custom class if exists
                >
                  {typeof column.header === 'function' ? column.header({} as any) : column.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="z-1 sticky top-10 overflow-y-hidden divide-y divide-gray-100">
            {isLoading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-20 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <div
                      className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mb-4"
                      style={{ animation: 'pulse 1.5s infinite ease-in-out' }}
                    >
                      <Loader2 size={32} className="animate-spin text-blue-600" />
                    </div>
                    <p className="text-lg font-medium text-gray-700">Loading {entityNamePlural}...</p>
                    <p className="text-sm text-gray-500 mt-2">Please wait while we fetch the {entityName} data</p>
                  </div>
                </td>
              </tr>
            ) : paginatedUsers.length > 0 ? (
              paginatedUsers.map((user) => (
                <tr
                  key={user.id}
                  className="hover:bg-gray-50 transition-colors duration-150"
                >
                  {columns.map(column => (
                    <td key={column.id || (column as any).accessorKey} className="py-3 px-4">
                      {typeof column.cell === 'function' ? column.cell({ row: { original: user } } as any) : (user as any)[(column as any).accessorKey]}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length} className="px-6 py-20 text-center">
                  {error ? (
                    <div
                      className="flex flex-col items-center justify-center"
                      style={{ animation: 'fadeIn 0.5s ease-in-out' }}
                    >
                      <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mb-5">
                        <AlertCircle size={40} className="text-gray-600" />
                      </div>
                      <div className="text-gray-700 font-medium text-xl mb-3">Error loading {entityNamePlural}</div>
                      <div className="text-sm text-gray-500 max-w-md mx-auto">{error}</div>
                    </div>
                  ) : hasActiveFilters ? (
                    <div
                      className="flex flex-col items-center justify-center"
                      style={{ animation: 'fadeIn 0.5s ease-in-out' }}
                    >
                      <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mb-5">
                        <Filter size={40} className="text-gray-600" />
                      </div>
                      <div className="text-gray-700 font-medium text-xl mb-3">No matching {entityNamePlural} found</div>
                      <div className="text-sm text-gray-500 mb-6">Try adjusting your search or filter criteria</div>
                      <button
                        onClick={clearFilters}
                        className="inline-flex items-center gap-2 px-5 py-2.5 bg-gray-700 text-white rounded-lg hover:bg-gray-800 font-medium"
                      >
                        <X size={18} />
                        Clear Filters
                      </button>
                    </div>
                  ) : (
                    <div
                      className="flex flex-col items-center justify-center"
                      style={{ animation: 'fadeIn 0.5s ease-in-out' }}
                    >
                      <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mb-5">
                        <Users size={40} className="text-gray-600" />
                      </div>
                      <div className="text-gray-700 font-medium text-xl mb-3">No {entityNamePlural} found</div>
                      <div className="text-sm text-gray-500 mb-6">Create a new {entityName} to get started with the system</div>
                      <a
                        href={createPath}
                        className="inline-flex items-center gap-2 px-5 py-2.5 bg-gray-700 text-white rounded-lg hover:bg-gray-800 font-medium"
                      >
                        <Users size={18} />
                        Create New {entityName.charAt(0).toUpperCase() + entityName.slice(1)}
                      </a>
                    </div>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {filteredUsers.length > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalPages={totalPages}
          rowsPerPage={rowsPerPage}
          totalItems={filteredUsers.length}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
        />
      )}

      {selectedUser && (
        <DeleteUserModal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          user={selectedUser}
          onSuccess={() => {
            // Consider a more robust way to update data, e.g., refetching or optimistic updates
            window.location.reload(); 
          }}
        />
      )}
    </div>
  );
};
