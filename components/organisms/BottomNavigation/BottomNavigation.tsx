'use client';

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { cn } from '@/utils/cn';
import { HomeIcon, ClipboardListIcon, Building2Icon, UserIcon, MenuIcon, ArrowLeft } from 'lucide-react';
import type { ComponentProps } from 'react';

export interface BottomNavigationItem {
  label: string;
  href: string;
  icon: React.ReactNode;
}

export interface BottomNavigationProps extends ComponentProps<'div'> {
  items: BottomNavigationItem[];
}

const BottomNavigation = ({ items, className, ...props }: BottomNavigationProps) => {
  const pathname = usePathname();
  const router = useRouter();

  // Check if we should show back button
  const showBackButton = pathname?.includes('/manage-worksheet') && pathname?.includes('type=create');

  // Get the most important items for bottom navigation (max 4-5 items)
  const getBottomNavItems = () => {
    const bottomNavItems: BottomNavigationItem[] = [];

    // Add back button if on create worksheet page
    if (showBackButton) {
      bottomNavItems.push({
        label: 'Back',
        href: '#',
        icon: <ArrowLeft className="w-5 h-5" />
      });
    }

    // Always add menu toggle
    bottomNavItems.push({
      label: 'Menu',
      href: '#',
      icon: <MenuIcon className="w-5 h-5" />
    });

    // Add remaining navigation items (adjust count based on back button)
    const maxItems = showBackButton ? 2 : 3;
    const importantItems = items.slice(0, maxItems);
    bottomNavItems.push(...importantItems);

    return bottomNavItems;
  };

  const bottomNavItems = getBottomNavItems();

  if (items.length === 0) return null;

  return (
    <div
      className={cn(
        'dock dock-sm fixed bottom-0 left-0 right-0 z-50 bg-base-100/95 backdrop-blur-sm border-t border-base-300 lg:hidden',
        'safe-area-inset-bottom', // Handle device safe areas
        className
      )}
      {...props}
    >
      {bottomNavItems.map((item, index) => {
        const isActive = pathname === item.href;
        const isMenuButton = item.label === 'Menu';
        const isBackButton = item.label === 'Back';

        if (isMenuButton) {
          return (
            <label
              key={index}
              htmlFor="dashboard-drawer"
              className={cn(
                'dock-item flex flex-col items-center justify-center p-2 min-h-[60px] transition-all duration-200 cursor-pointer',
                'hover:bg-base-200 active:bg-base-300 active:scale-95'
              )}
              aria-label="Toggle menu"
            >
              <span className="flex-shrink-0 mb-1 transition-transform duration-200 hover:scale-105">
                {item.icon}
              </span>
              <span className="text-xs font-medium text-base-content/70">
                {item.label}
              </span>
            </label>
          );
        }

        if (isBackButton) {
          return (
            <button
              key={index}
              onClick={() => router.push('/manage-worksheet')}
              className={cn(
                'dock-item flex flex-col items-center justify-center p-2 min-h-[60px] transition-all duration-200 cursor-pointer',
                'hover:bg-base-200 active:bg-base-300 active:scale-95'
              )}
              aria-label="Go back to worksheets"
            >
              <span className="flex-shrink-0 mb-1 transition-transform duration-200 hover:scale-105">
                {item.icon}
              </span>
              <span className="text-xs font-medium text-base-content/70">
                {item.label}
              </span>
            </button>
          );
        }

        return (
          <Link
            key={index}
            href={item.href}
            className={cn(
              'dock-item flex flex-col items-center justify-center p-2 min-h-[60px] transition-all duration-200',
              isActive
                ? 'dock-active bg-primary/10 text-primary'
                : 'text-base-content/70 hover:bg-base-200 hover:text-base-content active:bg-base-300 active:scale-95'
            )}
          >
            <span className={cn(
              'flex-shrink-0 mb-1 transition-transform duration-200',
              isActive ? 'scale-110' : 'hover:scale-105'
            )}>
              {item.icon}
            </span>
            <span className="text-xs font-medium">
              {item.label}
            </span>
          </Link>
        );
      })}
    </div>
  );
};

export default BottomNavigation;
