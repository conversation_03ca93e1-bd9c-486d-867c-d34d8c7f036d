'use client';

import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useOnboarding } from './OnboardingContext';
import { SchoolCreationForm } from '@/components/organisms/SchoolCreationForm';
import { Button } from '@/components/atoms/Button/Button';

export default function SchoolDetailsStep() {
  const router = useRouter();
  const { data: session } = useSession();
  const {
    state,
    isInitialized,
    setSchoolData,
    markStepCompleted,
    setError,
    getNextStep,
    fetchSchoolData,
    retrySchoolFetch
  } = useOnboarding();
  const { isLoading, schoolFetchStatus, schoolFetchError, schoolId, hasFullSchoolData } = state;

  // Check if user already has school data and auto-advance
  useEffect(() => {
  
    // Check if this step is already completed (from context initialization)
    const schoolDetailsStep = state.steps.find(step => step.id === 'school-details');
    const isStepCompleted = schoolDetailsStep?.isCompleted;

    // Case 1: User has both schoolId and school object (complete data)
    if (session?.user?.schoolId && session?.user?.school && !isStepCompleted) {
    
      // Mark this step as completed
      markStepCompleted('school-details');

      // Set school data in context if not already set
      if (!state.schoolData) {
        setSchoolData({
          name: session.user.school.name,
          address: session.user.school.address || '',
          phone: session.user.school.phoneNumber || '',
          registeredNumber: session.user.school.registeredNumber || '',
          email: session.user.school.email || '',
        });
      }

      // Navigate to next step
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    }
    // Case 2: User has schoolId but no school object - need to fetch
    else if (session?.user?.schoolId && !session?.user?.school && schoolFetchStatus === 'idle') {
      fetchSchoolData();
    }
    // Case 3: School data fetch completed successfully
    else if (schoolFetchStatus === 'success' && !isStepCompleted) {
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    }
    // Case 4: Step already completed, advance
    else if (isStepCompleted) {
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.schoolId, session?.user?.school, isInitialized, state.steps, state.schoolData, schoolFetchStatus]);

  const handleSchoolCreated = () => {
    markStepCompleted('school-details');

    // Force a router refresh to ensure middleware gets updated session
    router.refresh();

    // Navigate to next step with a small delay to ensure session is updated
    const nextStep = getNextStep();
    if (nextStep) {
      setTimeout(() => {
        router.push(`/onboarding/${nextStep}`);
      }, 1000); // Give time for session to propagate
    }
  };

  const handleSchoolCreationError = (error: string) => {
    setError(error);
  };

  const handleSkip = () => {
    // Allow skipping school creation for now, but mark as incomplete
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  // Show loading state while fetching school data
  if (schoolFetchStatus === 'loading') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="space-y-4 text-center">
          <div className="loading loading-spinner loading-lg mx-auto"></div>
          <h3 className="text-lg font-semibold">Loading your school information...</h3>
          <p className="text-gray-600">Please wait while we retrieve your school details.</p>
        </div>
      </motion.div>
    );
  }

  // Show error state if school data fetch failed
  if (schoolFetchStatus === 'error' && schoolFetchError) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="space-y-4">
          <div className="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h3 className="font-bold">Failed to load school information</h3>
              <div className="text-xs">{schoolFetchError}</div>
            </div>
          </div>

          <div className="flex gap-4 justify-center">
            <Button
              onClick={retrySchoolFetch}
              className="btn-primary"
            >
              Retry
            </Button>
            <Button
              variant="ghost"
              onClick={handleSkip}
              className="text-gray-600 hover:text-gray-800"
            >
              Skip for now
            </Button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-4">
        {/* School Creation Form */}
        <SchoolCreationForm
          onSuccess={handleSchoolCreated}
          onError={handleSchoolCreationError}
        />

        {/* Skip Option */}
        <div className="flex justify-between items-center mt-8 pt-3 border-t border-gray-200">
          <Button
            variant="ghost"
            onClick={handleSkip}
            disabled={isLoading}
            className="text-gray-600 hover:text-gray-800"
          >
            Skip for now
          </Button>

          <div className="text-sm text-gray-500">
            Complete later in dashboard
          </div>
        </div>
      </div>
    </motion.div>
  );
}
