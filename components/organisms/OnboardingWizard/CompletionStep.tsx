'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { CheckCircle, ArrowRight, School, User, Palette, ArrowLeft } from 'lucide-react';
import { useOnboarding } from './OnboardingContext';
import { Button } from '@/components/atoms/Button/Button';

export default function CompletionStep() {
  const router = useRouter();
  const { update: updateSession } = useSession();
  const {
    state,
    markStepCompleted,
    resetOnboarding,
    getPreviousStep
  } = useOnboarding();

  const [isRedirecting, setIsRedirecting] = useState(false);
  const [countdown, setCountdown] = useState(5);

  const { schoolData, brandingData, profileData } = state;

  const handleGoToDashboard = useCallback(async () => {
    setIsRedirecting(true);

    try {
      // Update session to reflect any changes (like schoolId)
      await updateSession();

      // Clear onboarding state
      resetOnboarding();

      // Redirect to dashboard
      router.push('/my-school');
      router.refresh();
    } catch (error) {
      console.error('Error redirecting to dashboard:', error);
      setIsRedirecting(false);
    }
  }, [updateSession, resetOnboarding, router]);

  useEffect(() => {
    // Mark completion step as completed
    markStepCompleted('complete');

    // Start countdown for auto-redirect
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          handleGoToDashboard();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [markStepCompleted, handleGoToDashboard]); // Add both memoized functions to dependencies

  const handleBack = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      router.push(`/onboarding/${previousStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-6 text-center">
        {/* Success Icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex justify-center">
            <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg">
              <CheckCircle size={40} className="text-white" />
            </div>
          </div>
        </motion.div>

        {/* Success Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-gray-800 mb-2">
            Welcome to EduSG! 🎉
          </h2>
          <p className="text-gray-600 text-lg">
            Your account has been set up successfully. You&apos;re ready to start your educational journey!
          </p>
        </motion.div>

        {/* Setup Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">Setup Summary</h3>
            
            <div className="grid gap-4 md:grid-cols-3">
              {/* School Details */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <School size={20} className="text-green-600" />
                  <div className="font-medium text-green-800">School Details</div>
                </div>
                <div className="text-sm text-green-600">
                  {schoolData?.name ? (
                    <>
                      <div className="font-medium">{schoolData.name}</div>
                      {schoolData.address && <div>{schoolData.address}</div>}
                    </>
                  ) : (
                    'Can be completed later'
                  )}
                </div>
              </div>

              {/* Branding */}
              <div className="bg-section-bg-accent border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <Palette size={20} className="text-link-default" />
                  <div className="font-medium text-text-primary">Branding</div>
                </div>
                <div className="text-sm text-link-default">
                  {brandingData?.theme ? (
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: brandingData.primaryColor }}
                      ></div>
                      <span className="capitalize">{brandingData.theme} theme</span>
                    </div>
                  ) : (
                    'Default theme applied'
                  )}
                </div>
              </div>

              {/* Profile */}
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <User size={20} className="text-purple-600" />
                  <div className="font-medium text-purple-800">Profile</div>
                </div>
                <div className="text-sm text-purple-600">
                  {profileData?.specialization || profileData?.bio ? (
                    <>
                      {profileData.specialization && (
                        <div>{profileData.specialization}</div>
                      )}
                      {profileData.experience && (
                        <div>{profileData.experience}</div>
                      )}
                    </>
                  ) : (
                    'Can be completed later'
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Next Steps */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">What&apos;s Next?</h3>
            <div className="text-left space-y-2 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Explore your school dashboard</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Complete any remaining school details</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Start creating educational content</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Invite students and manage your school</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <div className="flex justify-between items-center pt-6 border-t border-gray-200">
            <Button
              variant="ghost"
              onClick={handleBack}
              disabled={isRedirecting}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft size={16} />
              <span>Back</span>
            </Button>

            <div className="space-y-4">
              <Button
                onClick={handleGoToDashboard}
                variant="primary"
                disabled={isRedirecting}
                className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
              >
                {isRedirecting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    <span>Redirecting...</span>
                  </>
                ) : (
                  <>
                    <span>Go to My School</span>
                    <ArrowRight size={16} />
                  </>
                )}
              </Button>

              <p className="text-sm text-gray-500">
                Automatically redirecting in {countdown} seconds...
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}
