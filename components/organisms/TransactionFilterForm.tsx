'use client';

import React, { useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Calendar, Filter, RotateCcw, Search } from 'lucide-react';
import { FormField } from '@/components/molecules/FormField/FormField';
import { Input } from '@/components/atoms/Input/Input';
import { ITransactionFilters } from '@/actions/transaction.action';

// ============================================================================
// ZOD VALIDATION SCHEMA
// ============================================================================

const TransactionFilterSchema = z.object({
  startDate: z.string(),
  endDate: z.string(),
  status: z.enum(['all', 'paid', 'failed', 'pending']),
}).refine((data) => {
  if (data.startDate && data.endDate && data.startDate !== '' && data.endDate !== '') {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: "Start date must be before or equal to end date",
  path: ["endDate"],
});

type TransactionFilterFormData = z.infer<typeof TransactionFilterSchema>;

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface TransactionFilterFormProps {
  onFilterSubmit: (filters: ITransactionFilters) => void;
  onClearFilters: () => void;
  initialFilters?: Partial<ITransactionFilters>;
  className?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function TransactionFilterForm({
  onFilterSubmit,
  onClearFilters,
  initialFilters,
  className = "",
}: TransactionFilterFormProps) {
  const [isPending, startTransition] = useTransition();

  // Initialize form with react-hook-form and zod validation
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isValid },
  } = useForm<TransactionFilterFormData>({
    resolver: zodResolver(TransactionFilterSchema),
    defaultValues: {
      startDate: initialFilters?.startDate ? initialFilters.startDate.split('T')[0] : '',
      endDate: initialFilters?.endDate ? initialFilters.endDate.split('T')[0] : '',
      status: (initialFilters?.status as 'all' | 'paid' | 'failed' | 'pending') || 'all',
    },
    mode: 'onChange',
  });

  // Watch form values to check if form has any active filters
  const formValues = watch();
  const hasActiveFilters = Boolean(
    (formValues.startDate && formValues.startDate !== '') || 
    (formValues.endDate && formValues.endDate !== '') || 
    formValues.status !== 'all'
  );

  // ============================================================================
  // FORM HANDLERS
  // ============================================================================

  const onSubmit = (data: TransactionFilterFormData) => {
    startTransition(() => {
      const filters: ITransactionFilters = {};
      
      // Add start date if provided
      if (data.startDate && data.startDate !== '') {
        filters.startDate = new Date(data.startDate).toISOString();
      }
      
      // Add end date if provided (set to end of day)
      if (data.endDate && data.endDate !== '') {
        const endDate = new Date(data.endDate);
        endDate.setHours(23, 59, 59, 999);
        filters.endDate = endDate.toISOString();
      }
      
      // Add status if not 'all'
      if (data.status !== 'all') {
        filters.status = data.status as 'paid' | 'failed' | 'pending';
      }
      
      onFilterSubmit(filters);
    });
  };

  const handleClearFilters = () => {
    startTransition(() => {
      reset({
        startDate: '',
        endDate: '',
        status: 'all',
      });
      onClearFilters();
    });
  };

  // ============================================================================
  // RENDER COMPONENT
  // ============================================================================

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-blue-50 rounded-lg">
          <Filter className="w-5 h-5 text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Filter Transactions</h3>
          <p className="text-sm text-gray-500">Filter your transaction history by date range and status</p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Date Range Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="From Date"
            icon={<Calendar className="w-4 h-4" />}
            error={errors.startDate?.message}
            hint="Select start date for filtering"
          >
            <Input
              type="date"
              {...register('startDate')}
              className="input"
              disabled={isPending}
            />
          </FormField>

          <FormField
            label="To Date"
            icon={<Calendar className="w-4 h-4" />}
            error={errors.endDate?.message}
            hint="Select end date for filtering"
          >
            <Input
              type="date"
              {...register('endDate')}
              className="input"
              disabled={isPending}
            />
          </FormField>
        </div>

        {/* Status Filter Section */}
        <FormField
          label="Transaction Status"
          error={errors.status?.message}
          hint="Filter by payment status"
        >
          <select
            {...register('status')}
            className="select w-full"
            disabled={isPending}
          >
            <option value="all">All Statuses</option>
            <option value="paid">Paid</option>
            <option value="failed">Failed</option>
            <option value="pending">Pending</option>
          </select>
        </FormField>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-100">
          <button
            type="submit"
            disabled={isPending || !isValid}
            className={`btn btn-primary flex-1 sm:flex-initial ${
              isPending ? 'loading' : ''
            }`}
          >
            {isPending ? (
              <>
                <span className="loading loading-spinner loading-sm"></span>
                Applying Filters...
              </>
            ) : (
              <>
                <Search className="w-4 h-4" />
                Apply Filters
              </>
            )}
          </button>

          <button
            type="button"
            onClick={handleClearFilters}
            disabled={isPending || !hasActiveFilters}
            className="btn btn-ghost flex-1 sm:flex-initial"
          >
            <RotateCcw className="w-4 h-4" />
            Clear Filters
          </button>
        </div>

        {/* Filter Summary */}
        {hasActiveFilters && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="p-1 bg-blue-100 rounded">
                <Filter className="w-3 h-3 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-blue-900 mb-1">Active Filters:</p>
                <div className="flex flex-wrap gap-2 text-xs">
                  {formValues.startDate && formValues.startDate !== '' && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                      From: {new Date(formValues.startDate).toLocaleDateString()}
                    </span>
                  )}
                  {formValues.endDate && formValues.endDate !== '' && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                      To: {new Date(formValues.endDate).toLocaleDateString()}
                    </span>
                  )}
                  {formValues.status !== 'all' && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded capitalize">
                      Status: {formValues.status}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
} 