'use client';

import React, { useState, useCallback } from 'react';
import { PackageDataTable } from '@/components/organisms/PackageDataTable/PackageDataTable';
import { PackageForm } from '@/components/organisms/PackageForm/PackageForm';
import { DeletePackageModal } from '@/components/molecules/DeletePackageModal';
import { IPackageResponse } from '@/apis/packageApi';
import { handleTogglePackageStatusAction } from '@/actions/package.action';

interface PackageManagementProps {
  className?: string;
}

export const PackageManagement: React.FC<PackageManagementProps> = ({
  className = '',
}) => {
  // Modal state management
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [selectedPackage, setSelectedPackage] = useState<IPackageResponse | null>(null);
  
  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [packageToDelete, setPackageToDelete] = useState<IPackageResponse | null>(null);
  
  // Refresh trigger for data table
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Handle create package
  const handleCreatePackage = useCallback(() => {
    setFormMode('create');
    setSelectedPackage(null);
    setIsFormModalOpen(true);
  }, []);

  // Handle edit package
  const handleEditPackage = useCallback((packageData: IPackageResponse) => {
    setFormMode('edit');
    setSelectedPackage(packageData);
    setIsFormModalOpen(true);
  }, []);

  // Handle delete package
  const handleDeletePackage = useCallback((packageData: IPackageResponse) => {
    setPackageToDelete(packageData);
    setIsDeleteModalOpen(true);
  }, []);

  // Handle toggle package status
  const handleToggleStatus = useCallback(async (packageData: IPackageResponse) => {
    const isActive = !packageData.deletedAt;
    
    try {
      const response = await handleTogglePackageStatusAction(packageData.id, isActive);
      
      if (response.status === 'success') {
        // Refresh the data table
        setRefreshTrigger(prev => prev + 1);
      } else {
        // Handle error - you could show a toast notification here
        console.error('Failed to toggle package status:', response.message);
        alert(response.message || 'Failed to toggle package status');
      }
    } catch (error: any) {
      console.error('Error toggling package status:', error);
      alert('An unexpected error occurred while toggling package status');
    }
  }, []);

  // Handle successful form submission
  const handleFormSuccess = useCallback(() => {
    // Refresh the data table by incrementing trigger
    setRefreshTrigger(prev => prev + 1);
    setIsFormModalOpen(false);
  }, []);

  // Handle modal close
  const handleModalClose = useCallback((open: boolean) => {
    setIsFormModalOpen(open);
    if (!open) {
      // Clear selected package when modal closes
      setSelectedPackage(null);
    }
  }, []);

  // Handle delete modal success
  const handleDeleteSuccess = useCallback(() => {
    // Refresh the data table by incrementing trigger
    setRefreshTrigger(prev => prev + 1);
    setIsDeleteModalOpen(false);
    setPackageToDelete(null);
  }, []);

  // Handle delete modal close
  const handleDeleteModalClose = useCallback(() => {
    setIsDeleteModalOpen(false);
    setPackageToDelete(null);
  }, []);

  return (
    <div className={className}>
      {/* Main Package Data Table */}
      <PackageDataTable
        key={refreshTrigger} // Force re-render when data changes
        onCreatePackage={handleCreatePackage}
        onEditPackage={handleEditPackage}
        onDeletePackage={handleDeletePackage}
        onToggleStatus={handleToggleStatus}
      />

      {/* Package Form Modal */}
      <PackageForm
        open={isFormModalOpen}
        onOpenChange={handleModalClose}
        mode={formMode}
        packageData={selectedPackage}
        onSuccess={handleFormSuccess}
      />

      {/* Delete Package Modal */}
      {packageToDelete && (
        <DeletePackageModal
          isOpen={isDeleteModalOpen}
          onClose={handleDeleteModalClose}
          package={packageToDelete}
          onSuccess={handleDeleteSuccess}
        />
      )}
    </div>
  );
}; 