'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { 
  Loader2, 
  Plus, 
  Edit, 
  Trash2, 
  AlertCircle,
  Package,
  DollarSign,
  XCircle,
  CheckCircle
} from 'lucide-react';

// Import custom components
import CustomTable from '@/components/molecules/CustomTable/CustomTable';
import { Button } from '@/components/atoms/Button/Button';
import { StatusBadge } from '@/components/atoms/StatusBadge/StatusBadge';
import { DropdownMenu, DropdownMenuItem } from '@/components/molecules/DropdownMenu/DropdownMenu';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { TablePagination } from '@/components/molecules/TablePagination/TablePagination';
import { AnimationStyles } from '@/components/atoms/AnimationStyles/AnimationStyles';
import { Tooltip } from '@/components/atoms/Tooltip/Tooltip';

// Import package actions and types
import { handleGetAllPackagesAction } from '@/actions/package.action';
import { IPackageResponse } from '@/apis/packageApi';

export interface PackageDataTableProps {
  onCreatePackage?: () => void;
  onEditPackage?: (packageData: IPackageResponse) => void;
  onDeletePackage?: (packageData: IPackageResponse) => void;
  onToggleStatus?: (packageData: IPackageResponse) => void;
}

// Helper to format price with billing cycle - updated to work with API response
const formatPrice = (unitAmount: number, interval?: string, currency: string = 'USD') => {
  const priceInDollars = unitAmount / 100; // Convert from cents to dollars
  const formattedPrice = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
    minimumFractionDigits: 0,
  }).format(priceInDollars);
  
  if (!interval) return formattedPrice;
  
  const intervalMap = {
    'day': 'day',
    'week': 'week', 
    'month': 'mo',
    'year': 'yr'
  };
  
  return `${formattedPrice}/${intervalMap[interval as keyof typeof intervalMap] || interval}`;
};

// Helper to get package status from deletedAt field
const getPackageStatus = (deletedAt: string | null): 'active' | 'inactive' => {
  return deletedAt ? 'inactive' : 'active';
};

// Helper to format API message
const formatApiMessage = (message: string | any[] | undefined): string => {
  if (Array.isArray(message)) {
    return message.map(m => typeof m === 'object' && m.message ? m.message : String(m)).join(', ');
  }
  return String(message || '');
};

export const PackageDataTable: React.FC<PackageDataTableProps> = ({
  onCreatePackage,
  onEditPackage,
  onDeletePackage,
  onToggleStatus,
}) => {
  // State management
  const [packages, setPackages] = useState<IPackageResponse[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [apiMessage, setApiMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // Data fetching
  const fetchPackages = useCallback(async () => {
    setIsLoading(true);
    setApiMessage(null);
    
    try {
      const response = await handleGetAllPackagesAction();
      console.log('response', response);
      if (response.status === 'success') {
        setPackages(response.data || []);
        setApiMessage(null); // Clear any previous error messages
      } else {
        setApiMessage({ 
          type: 'error', 
          message: formatApiMessage(response.message) || 'Failed to fetch packages.' 
        });
        setPackages([]);
      }
    } catch (error: any) {
      setApiMessage({ 
        type: 'error', 
        message: formatApiMessage(error.message) || 'An unexpected error occurred while fetching packages.' 
      });
      setPackages([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch data on component mount
  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  // Handle action menu clicks
  const handleEditClick = useCallback((packageData: IPackageResponse) => {
    onEditPackage?.(packageData);
  }, [onEditPackage]);

  const handleDeleteClick = useCallback((packageData: IPackageResponse) => {
    onDeletePackage?.(packageData);
  }, [onDeletePackage]);

  const handleToggleStatusClick = useCallback((packageData: IPackageResponse) => {
    onToggleStatus?.(packageData);
  }, [onToggleStatus]);

  // Table column configuration
  const columns = useMemo<ColumnDef<IPackageResponse>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-gray-500" />
          <div>
            <div className="font-medium text-gray-900">{row.original.name}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'description',
      header: 'Description',
      cell: ({ row }) => (
        <div className="max-w-xs truncate text-gray-600">
          {row.original.description || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'prices',
      header: 'Price',
      cell: ({ row }) => {
        const firstPrice = row.original.prices?.[0];
        if (!firstPrice) {
          return (
            <div className="flex items-center gap-1">
              <DollarSign className="h-4 w-4 text-gray-400" />
              <span className="text-gray-500">No pricing</span>
            </div>
          );
        }
        return (
          <div className="flex items-center gap-1">
            <DollarSign className="h-4 w-4 text-green-500" />
            <span className="font-medium text-gray-900">
              {formatPrice(firstPrice.unitAmount, firstPrice.interval, firstPrice.currency)}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'deletedAt',
      header: 'Status',
      cell: ({ row }) => (
        <StatusBadge 
          status={getPackageStatus(row.original.deletedAt)}
        />
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const packageData = row.original;
        const isActive = !packageData.deletedAt;
        
        return (
          <div className="flex items-center gap-2">
            {/* Edit Button */}
            <Tooltip content="Edit package">
              <Button
                variant="ghost"
                onClick={() => handleEditClick(packageData)}
                className="h-8 !min-w-8 p-0 hover:bg-gray-100"
              >
                <Edit className="h-4 w-4" />
              </Button>
            </Tooltip>
            
            {/* Toggle Status Button */}
            <Tooltip content={isActive ? 'Deactivate package' : 'Activate package'}>
              <Button
                variant="ghost"
                onClick={() => handleToggleStatusClick(packageData)}
                className={`h-8 !min-w-8 p-0 ${
                  isActive 
                    ? 'hover:bg-red-50 text-red-600 hover:text-red-700' 
                    : 'hover:bg-green-50 text-green-600 hover:text-green-700'
                }`}
              >
                {isActive ? (
                  <XCircle className="h-4 w-4" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
              </Button>
            </Tooltip>
            
            {/* Delete Button */}
            <Tooltip content="Delete package">
              <Button
                variant="ghost"
                onClick={() => handleDeleteClick(packageData)}
                className="h-8 !min-w-8 p-0 hover:bg-red-50 text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </Tooltip>
          </div>
        );
      },
    },
  ], [handleEditClick, handleDeleteClick, handleToggleStatusClick]);

  // Calculate pagination info
  const startIndex = pagination.pageIndex * pagination.pageSize;
  const endIndex = Math.min(startIndex + pagination.pageSize, packages.length);
  const paginatedData = packages.slice(startIndex, endIndex);
  const totalPages = Math.ceil(packages.length / pagination.pageSize);

  return (
    <div className="space-y-4">
      {/* Header with Create Button */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Packages</h2>
          <p className="text-sm text-gray-600">
            Manage subscription packages and pricing plans
          </p>
        </div>
        <Button
          onClick={onCreatePackage}
          className="flex items-center gap-2"
          variant="primary"
          iconProps={{
            variant: 'plus',
            className: 'h-4 w-4'
          }}
        >
          Create Package
        </Button>
      </div>

             {/* API Message */}
       {apiMessage && (
         <AlertMessage
           type={apiMessage.type}
           message={apiMessage.message}
         />
       )}

      {/* Data Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <CustomTable
          columns={columns}
          tableData={paginatedData}
          isLoading={isLoading}
          manualPagination={true}
          pageCount={totalPages}
          pagination={pagination}
          onPaginationChange={setPagination}
        />
        
        {/* Table Pagination */}
        {!isLoading && packages.length > 0 && (
          <div className="border-t border-gray-200 px-4 py-3">
                         <TablePagination
               currentPage={pagination.pageIndex + 1}
               totalPages={totalPages}
               totalItems={packages.length}
               rowsPerPage={pagination.pageSize}
               onPageChange={(page) => setPagination(prev => ({ ...prev, pageIndex: page - 1 }))}
               onRowsPerPageChange={(e) => setPagination({ pageIndex: 0, pageSize: parseInt(e.target.value) })}
             />
          </div>
        )}
      </div>

      {/* Empty State */}
      {!isLoading && packages.length === 0 && !apiMessage && (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No packages</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new package.
          </p>
        </div>
      )}
    </div>
  );
}; 