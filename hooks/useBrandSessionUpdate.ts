'use client';

import { useSession } from 'next-auth/react';
import { IBrandResponse } from '@/apis/brandApi';

/**
 * Custom hook for updating the Next Auth session with brand data
 * 
 * @returns An object containing:
 * - updateSessionBrand: Function to update the session with brand data
 * - session: The current session data
 * - status: The session status ('loading', 'authenticated', 'unauthenticated')
 */
export function useBrandSessionUpdate() {
  const { data: session, update, status } = useSession();

  /**
   * Updates the Next Auth session with brand data
   * This updates the brand information within the user's school object
   * 
   * @param brandData - The updated brand data from the API
   * @returns A promise that resolves when the session has been updated
   */
  const updateSessionBrand = async (brandData: IBrandResponse) => {
    if (!brandData) {
      console.error('Cannot update session: No brand data provided');
      return false;
    }

    if (!session?.user?.school) {
      console.error('Cannot update session: User has no school in session');
      return false;
    }

    try {
      // Update the session with the new brand data
      await update({
        user: {
          school: {
            ...session.user.school,
            brand: {
              id: brandData.id,
              logo: brandData.logo,
              color: brandData.color,
              image: brandData.image,
            }
          }
        }
      });
      
      console.log('✅ Session updated with brand data:', brandData.id);
      return true;
    } catch (error) {
      console.error('❌ Failed to update session with brand data:', error);
      return false;
    }
  };

  /**
   * Removes brand data from the session
   * This sets the brand to undefined in the user's school object
   * 
   * @returns A promise that resolves when the session has been updated
   */
  const removeSessionBrand = async () => {
    if (!session?.user?.school) {
      console.error('Cannot update session: User has no school in session');
      return false;
    }

    try {
      // Remove the brand from the session
      await update({
        user: {
          school: {
            ...session.user.school,
            brand: undefined
          }
        }
      });
      
      console.log('✅ Brand removed from session');
      return true;
    } catch (error) {
      console.error('❌ Failed to remove brand from session:', error);
      return false;
    }
  };

  return {
    updateSessionBrand,
    removeSessionBrand,
    session,
    status
  };
}
